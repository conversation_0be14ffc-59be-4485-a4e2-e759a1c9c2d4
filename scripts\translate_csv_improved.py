#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import re

def translate_text(text):
    """
    翻译中文文本到英文
    """
    if pd.isna(text) or text == '':
        return text
    
    # 更完整的翻译字典
    translations = {
        # 基础术语
        '分类级别': 'Classification Level',
        '分类代码': 'Classification Code', 
        '分类名称': 'Classification Name',
        '上级代码': 'Parent Code',
        '核心关键词': 'Core Keywords',
        '说明': 'Description',
        
        # 分类级别
        '门类': 'Sector',
        '大类': 'Major Category',
        '中类': 'Medium Category', 
        '小类': 'Minor Category',
        
        # 基础词汇
        '农业生产': 'Agricultural Production',
        '农作物': 'Crops',
        '作物生产': 'Crop Production',
        '农产品': 'Agricultural Products',
        '本门类包括': 'This sector includes',
        '本类包括': 'This category includes',
        '指对': 'Refers to',
        '指以': 'Refers to',
        '指用于': 'Refers to',
        '指在': 'Refers to',
        '指为': 'Refers to',
        '指从事': 'Refers to engaging in',
        '指应用': 'Refers to applying',
        '指通过': 'Refers to through',
        '指利用': 'Refers to using',
        '指主要': 'Refers to mainly',
        '指将': 'Refers to converting',
        '指把': 'Refers to converting',
        '指各种': 'Refers to various',
        '各种': 'Various',
        '以及': 'And',
        '包括': 'Including',
        '等': 'etc.',
        '等活动': 'and other activities',
        '等生产活动': 'and other production activities',
        '的生产活动': 'production activities',
        '的活动': 'activities',
        '的种植': 'cultivation',
        '的开采': 'mining',
        '的加工': 'processing',
        '的制造': 'manufacturing',
        '的采集': 'collection',
        '的饲养': 'raising',
        '的养殖': 'breeding',
        '的捕捞': 'fishing',
        '的采运': 'harvesting and transportation',
        '种植': 'Cultivation',
        '开采': 'Mining',
        '加工': 'Processing',
        '制造': 'Manufacturing',
        '采集': 'Collection',
        '饲养': 'Raising',
        '养殖': 'Breeding',
        '捕捞': 'Fishing',
        '采运': 'Harvesting and Transportation',
        '生产': 'Production',
        '经营': 'Operation',
        '管理': 'Management',
        '服务': 'Service',
        '活动': 'Activities',
        '不包括': 'Does not include',
        '不含': 'Does not include',
        '还包括': 'Also includes',
        '也包括': 'Also includes',
        '以及其他': 'And other',
        '其他': 'Other',
        '未列明': 'Not listed elsewhere',
        '未列明的': 'Not listed elsewhere',
        '其他未列明': 'Other not listed elsewhere',
        '一般': 'General',
        '主要': 'Main',
        '专门': 'Specialized',
        '天然': 'Natural',
        '人工': 'Artificial',
        '野生': 'Wild',
        '栽培': 'Cultivation',
        '培育': 'Breeding',
        '繁殖': 'Reproduction',
        '收获': 'Harvest',
        '采伐': 'Logging',
        '宰杀': 'Slaughter',
        '捕获': 'Capture',
        '提取': 'Extraction',
        '精制': 'Refining',
        '初级': 'Primary',
        '初加工': 'Primary Processing',
        '深加工': 'Deep Processing',
        '精加工': 'Fine Processing',
        '综合': 'Comprehensive',
        '利用': 'Utilization',
        '处理': 'Treatment',
        '分类': 'Classification',
        '分级': 'Grading',
        '选矿': 'Beneficiation',
        '洗选': 'Washing and Selection',
        '运输': 'Transportation',
        '储存': 'Storage',
        '仓储': 'Warehousing',
        '配送': 'Distribution',
        '销售': 'Sales',
        '批发': 'Wholesale',
        '零售': 'Retail',
        '贸易': 'Trade',
        '进出口': 'Import and Export',
        '技术': 'Technology',
        '科学': 'Science',
        '研究': 'Research',
        '开发': 'Development',
        '设计': 'Design',
        '咨询': 'Consulting',
        '培训': 'Training',
        '教育': 'Education',
        '卫生': 'Health',
        '医疗': 'Medical',
        '社会': 'Social',
        '公共': 'Public',
        '文化': 'Culture',
        '体育': 'Sports',
        '娱乐': 'Entertainment',
        '旅游': 'Tourism',
        '交通': 'Transportation',
        '通信': 'Communication',
        '信息': 'Information',
        '软件': 'Software',
        '网络': 'Network',
        '金融': 'Finance',
        '保险': 'Insurance',
        '银行': 'Banking',
        '证券': 'Securities',
        '房地产': 'Real Estate',
        '租赁': 'Leasing',
        '商务': 'Business',
        '住宿': 'Accommodation',
        '餐饮': 'Catering',
        '环境': 'Environment',
        '环保': 'Environmental Protection',
        '能源': 'Energy',
        '电力': 'Electricity',
        '热力': 'Heat',
        '燃气': 'Gas',
        '水': 'Water',
        '供应': 'Supply',
        '建筑': 'Construction',
        '工程': 'Engineering',
        '施工': 'Construction',
        '装饰': 'Decoration',
        '设备': 'Equipment',
        '机械': 'Machinery',
        '工具': 'Tools',
        '仪器': 'Instruments',
        '材料': 'Materials',
        '原料': 'Raw Materials',
        '产品': 'Products',
        '商品': 'Goods',
        '物品': 'Items',
        '用品': 'Supplies',
        '制品': 'Products',
        '成品': 'Finished Products',
        '半成品': 'Semi-finished Products',
        '零件': 'Parts',
        '部件': 'Components',
        '配件': 'Accessories',
        '工业': 'Industry',
        '产业': 'Industry',
        '行业': 'Industry',
        '企业': 'Enterprise',
        '公司': 'Company',
        '厂': 'Factory',
        '场': 'Field',
        '站': 'Station',
        '中心': 'Center',
        '基地': 'Base',
        '园区': 'Park',
        '系统': 'System',
        '平台': 'Platform',
        '网点': 'Network',
        '机构': 'Institution',
        '组织': 'Organization',
        '协会': 'Association',
        '学会': 'Society',
        '联合会': 'Federation',
        '委员会': 'Committee',
        '办公室': 'Office',
        '部门': 'Department',
        
        # 具体行业术语
        '农、林、牧、渔业': 'Agriculture, Forestry, Animal Husbandry, and Fishery',
        '农业': 'Agriculture',
        '林业': 'Forestry', 
        '畜牧业': 'Animal Husbandry',
        '渔业': 'Fishery',
        '种植业': 'Planting Industry',
        '养殖业': 'Breeding Industry',
        '谷物': 'Grain',
        '稻谷': 'Rice',
        '小麦': 'Wheat',
        '玉米': 'Corn',
        '大豆': 'Soybean',
        '豆类': 'Legumes',
        '油料': 'Oil Crops',
        '薯类': 'Tubers',
        '棉花': 'Cotton',
        '麻类': 'Hemp',
        '糖料': 'Sugar Crops',
        '烟草': 'Tobacco',
        '蔬菜': 'Vegetables',
        '食用菌': 'Edible Fungi',
        '花卉': 'Flowers',
        '园艺': 'Horticulture',
        '水果': 'Fruits',
        '仁果类': 'Pome Fruits',
        '核果类': 'Stone Fruits',
        '葡萄': 'Grapes',
        '柑橘类': 'Citrus',
        '香蕉': 'Bananas',
        '亚热带': 'Subtropical',
        '坚果': 'Nuts',
        '含油果': 'Oil-bearing Fruits',
        '香料': 'Spices',
        '饮料': 'Beverages',
        '茶叶': 'Tea',
        '中药材': 'Traditional Chinese Medicine Materials',
        '中草药': 'Traditional Chinese Herbal Medicine',
        '草': 'Grass',
        '牧草': 'Pasture Grass',
        '草原': 'Grassland',
        '刈割': 'Cutting',
        '林木': 'Forest Trees',
        '育种': 'Breeding',
        '育苗': 'Seedling Raising',
        '苗木': 'Seedlings',
        '造林': 'Afforestation',
        '更新': 'Renewal',
        '森林': 'Forest',
        '管护': 'Management and Protection',
        '改培': 'Transformation',
        '木材': 'Wood',
        '竹材': 'Bamboo',
        '竹木': 'Bamboo and Wood',
        '林产品': 'Forest Products',
        '牲畜': 'Livestock',
        '家禽': 'Poultry',
        '动物': 'Animals',
        '牛': 'Cattle',
        '马': 'Horses',
        '猪': 'Pigs',
        '羊': 'Sheep',
        '骆驼': 'Camels',
        '鸡': 'Chickens',
        '鸭': 'Ducks',
        '鹅': 'Geese',
        '兔': 'Rabbits',
        '蜜蜂': 'Bees',
        '狩猎': 'Hunting',
        '捕捉': 'Capturing',
        '畜禽': 'Livestock and Poultry',
        '水产': 'Aquatic Products',
        '海水': 'Seawater',
        '内陆': 'Inland',
        '淡水': 'Freshwater',
        '海洋': 'Marine',
        '水域': 'Waters',
        '鱼类': 'Fish',
        '虾类': 'Shrimp',
        '贝类': 'Shellfish',
        '藻类': 'Algae',
        '专业': 'Professional',
        '辅助性': 'Auxiliary',
        '种子': 'Seeds',
        '种苗': 'Seedlings',
        '机械': 'Machinery',
        '灌溉': 'Irrigation',
        '排水': 'Drainage',
        '初加工': 'Primary Processing',
        '病虫害': 'Pests and Diseases',
        '防治': 'Prevention and Control',
        '代耕': 'Contract Farming',
        '代种': 'Contract Planting',
        '代收': 'Contract Harvesting',
        '托管': 'Trusteeship',
        '有害生物': 'Harmful Organisms',
        '防火': 'Fire Prevention',
        '繁殖': 'Reproduction',
        '圈舍': 'Pens',
        '清理': 'Cleaning',
        '免疫': 'Immunization',
        '接种': 'Vaccination',
        '标识': 'Identification',
        '诊疗': 'Diagnosis and Treatment',
        '良种': 'Improved Varieties',
        '粪污': 'Manure',
        '鱼苗': 'Fish Fry',
        '鱼种': 'Fish Seed',
        '增殖': 'Enhancement',
        
        # 采矿业
        '采矿业': 'Mining Industry',
        '矿业': 'Mining',
        '资源': 'Resources',
        '矿物': 'Minerals',
        '矿产': 'Mineral Resources',
        '矿床': 'Ore Deposits',
        '矿井': 'Mines',
        '矿山': 'Mining Areas',
        '地下': 'Underground',
        '地上': 'Surface',
        '露天': 'Open-pit',
        '煤炭': 'Coal',
        '烟煤': 'Bituminous Coal',
        '无烟煤': 'Anthracite',
        '褐煤': 'Lignite',
        '洗选': 'Washing and Selection',
        '石油': 'Oil',
        '天然气': 'Natural Gas',
        '陆地': 'Onshore',
        '海洋': 'Offshore',
        '瓦斯': 'Gas',
        '煤层气': 'Coalbed Methane',
        '可燃冰': 'Gas Hydrates',
        '黑色金属': 'Ferrous Metals',
        '有色金属': 'Non-ferrous Metals',
        '非金属': 'Non-metals',
        '铁矿': 'Iron Ore',
        '锰矿': 'Manganese Ore',
        '铬矿': 'Chromium Ore',
        '铜矿': 'Copper Ore',
        '铅锌矿': 'Lead-zinc Ore',
        '镍钴矿': 'Nickel-cobalt Ore',
        '锡矿': 'Tin Ore',
        '锑矿': 'Antimony Ore',
        '铝矿': 'Aluminum Ore',
        '镁矿': 'Magnesium Ore',
        '金矿': 'Gold Ore',
        '银矿': 'Silver Ore',
        '贵金属': 'Precious Metals',
        '稀有': 'Rare',
        '稀土': 'Rare Earth',
        '钨钼矿': 'Tungsten-molybdenum Ore',
        '放射性': 'Radioactive',
        '土砂石': 'Earth and Stone',
        '石灰石': 'Limestone',
        '石膏': 'Gypsum',
        '石材': 'Stone',
        '粘土': 'Clay',
        '化学矿': 'Chemical Minerals',
        '盐': 'Salt',
        '石棉': 'Asbestos',
        '云母': 'Mica',
        '石墨': 'Graphite',
        '滑石': 'Talc',
        '宝石': 'Gems',
        '玉石': 'Jade',
        '选矿': 'Beneficiation',
        
        # 制造业
        '制造业': 'Manufacturing',
        '制造': 'Manufacturing',
        '加工': 'Processing',
        '生产': 'Production',
        '工业': 'Industry',
        '企业': 'Enterprise',
        '食品': 'Food',
        '农副食品': 'Agricultural and Food Products',
        '磨制': 'Milling',
        '饲料': 'Feed',
        '宠物': 'Pet',
        '植物油': 'Vegetable Oil',
        '食用': 'Edible',
        '非食用': 'Non-edible',
        '制糖': 'Sugar Manufacturing',
        '屠宰': 'Slaughter',
        '肉类': 'Meat',
        '肉制品': 'Meat Products',
        '副产品': 'By-products',
        '水产品': 'Aquatic Products',
        '冷冻': 'Freezing',
        '冷藏': 'Refrigeration',
        '鱼糜': 'Fish Paste',
        '干制': 'Drying',
        '腌制': 'Curing',
        '鱼油': 'Fish Oil',
        '提取': 'Extraction',
        '菌类': 'Fungi',
        '脱水': 'Dehydration',
        '干燥': 'Drying',
        '淀粉': 'Starch',
        '豆制品': 'Bean Products',
        '蛋品': 'Egg Products',
        '焙烤': 'Baking',
        '糕点': 'Pastries',
        '面包': 'Bread',
        '饼干': 'Biscuits',
        '糖果': 'Candy',
        '巧克力': 'Chocolate',
        '蜜饯': 'Preserved Fruits',
        '方便食品': 'Convenience Foods',
        '速冻': 'Quick-frozen',
        '方便面': 'Instant Noodles',
        '乳制品': 'Dairy Products',
        '奶粉': 'Milk Powder',
        '炼乳': 'Condensed Milk',
        '干酪': 'Cheese',
        
        # 数字
        '01': '01',
        '02': '02',
        '03': '03',
        '04': '04',
        '05': '05',
        '06': '06',
        '07': '07',
        '08': '08',
        '09': '09',
        '10': '10',
        '11': '11',
        '12': '12',
        '13': '13',
        '14': '14',
        '15': '15',
        '16': '16',
        '17': '17',
        '18': '18',
        '19': '19',
        '20': '20',
        '一': 'one',
        '二': 'two',
        '三': 'three',
        '四': 'four',
        '五': 'five',
        '六': 'six',
        '七': 'seven',
        '八': 'eight',
        '九': 'nine',
        '十': 'ten',
        '个': '',
        '种': 'types of',
        '类': 'types',
        '项': 'items',
        '批': 'batches',
        '次': 'times',
        '年': 'years',
        '月': 'months',
        '日': 'days',
        '时': 'hours',
        '分': 'minutes',
        '秒': 'seconds',
    }
    
    # 先处理分号分隔的关键词
    if ';' in str(text):
        keywords = str(text).split(';')
        translated_keywords = []
        for keyword in keywords:
            keyword = keyword.strip()
            # 对每个关键词进行翻译
            translated_keyword = keyword
            for chinese, english in sorted(translations.items(), key=lambda x: len(x[0]), reverse=True):
                if chinese in translated_keyword:
                    translated_keyword = translated_keyword.replace(chinese, english)
            translated_keywords.append(translated_keyword)
        return ';'.join(translated_keywords)
    
    # 处理普通文本
    result = str(text).strip()
    
    # 按长度排序，先替换长的词汇
    for chinese, english in sorted(translations.items(), key=lambda x: len(x[0]), reverse=True):
        if chinese in result:
            result = result.replace(chinese, english)
    
    return result

def translate_csv_file(input_file, output_file):
    """
    翻译CSV文件
    """
    print(f"正在读取文件: {input_file}")
    
    # 读取CSV文件
    try:
        df = pd.read_csv(input_file, encoding='utf-8')
    except UnicodeDecodeError:
        try:
            df = pd.read_csv(input_file, encoding='gbk')
        except UnicodeDecodeError:
            df = pd.read_csv(input_file, encoding='gb2312')
    
    print(f"文件包含 {len(df)} 行数据")
    
    # 翻译每一列
    for column in df.columns:
        print(f"正在翻译列: {column}")
        df[column] = df[column].apply(translate_text)
    
    # 保存翻译后的文件
    print(f"正在保存翻译后的文件: {output_file}")
    df.to_csv(output_file, index=False, encoding='utf-8')
    
    print(f"翻译完成！共处理了 {len(df)} 行数据")

if __name__ == "__main__":
    input_file = "GB_T_4754_2017_converted.csv"
    output_file = "GB_T_4754_2017_converted_english_complete.csv"
    
    translate_csv_file(input_file, output_file)