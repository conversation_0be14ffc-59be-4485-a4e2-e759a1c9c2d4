# ISIC Rev.5 Extraction Scripts

This folder contains Python scripts for extracting and converting ISIC Rev.5 classification data from Excel format to standardized CSV format.

## Scripts Overview

### Main Scripts

#### `extract_isic_rev5_clean.py` ⭐ **RECOMMENDED**
- **Purpose**: Final working script for ISIC Rev.5 data extraction
- **Features**: 
  - Extracts data from `ISIC5_Exp_Notes_11Mar2024.xlsx`
  - Converts to English format with proper classification levels
  - Cleans special characters and formatting issues
  - Outputs to `final_data/ISIC_Rev5/ISIC_Rev5_converted.csv`
- **Output Format**:
  - Classification Level (Section/Division/Group/Class)
  - Classification Code (A, A01, A011, A0111, etc.)
  - Classification Name (English titles)
  - Parent Code (hierarchical relationships)
  - Core Keywords (extracted from titles)
  - Description (cleaned explanatory text)

#### `extract_isic_rev5_improved.py`
- **Purpose**: Improved version with better column mapping
- **Features**: Uses "ISIC Rev 5 Code (with Section)" for proper classification
- **Status**: Working but superseded by clean version

#### `extract_isic_rev5_final.py`
- **Purpose**: Version with enhanced text cleaning
- **Features**: Advanced regex cleaning for special characters
- **Status**: Alternative implementation

#### `extract_isic_rev5.py`
- **Purpose**: Initial extraction script
- **Features**: Basic extraction functionality
- **Status**: Early version, use clean version instead

## Usage

### Running the Main Script

```bash
cd /path/to/Classification
python scripts/extract_isic_rev5_clean.py
```

**Note**: If the output file is locked (e.g., open in Excel), the script will automatically create a timestamped version.

### Requirements
- pandas
- openpyxl (for Excel file reading)
- re (regex, built-in)
- os (built-in)

### Input File
- `raw_data/ISIC5_Exp_Notes_11Mar2024.xlsx`

### Output File
- `final_data/ISIC_Rev5/ISIC_Rev5_converted.csv`

## Output Statistics

The conversion produces:
- **Total Records**: 830
- **Section**: 22 records (A, B, C, etc.)
- **Division**: 87 records (A01, A02, etc.)
- **Group**: 258 records (A011, A012, etc.)
- **Class**: 463 records (A0111, A0112, etc.)

## Data Structure

The output CSV follows the same structure as GB_T_4754_2017_converted.csv but with English headers:

| Column | Description | Example |
|--------|-------------|---------|
| Classification Level | Hierarchical level | Section, Division, Group, Class |
| Classification Code | ISIC code | A, A01, A011, A0111 |
| Classification Name | English title | Agriculture, forestry and fishing |
| Parent Code | Parent classification code | A0 (for A01) |
| Core Keywords | Extracted keywords | Agriculture; forestry and fishing |
| Description | Detailed explanation | This section includes... |

## Notes

- All scripts are designed to work from the project root directory
- The clean version handles Unicode characters and special formatting properly
- Output uses UTF-8 encoding with BOM for Excel compatibility
- Parent codes are automatically calculated based on code hierarchy
