import pandas as pd
import os

def extract_isic_rev5_data():
    """
    提取ISIC Rev.5分类数据，转换为类似GB_T_4754_2017_converted.csv的格式
    """
    
    # 检查ISIC文件路径
    isic_paths = [
        'raw_data/ISIC5_Exp_Notes_11Mar2024.xlsx',
        'final_data/ISIC_Rev5/ISIC5_Exp_Notes_11Mar2024.xlsx'
    ]
    
    isic_file = None
    for path in isic_paths:
        if os.path.exists(path):
            isic_file = path
            break
    
    if not isic_file:
        print("未找到ISIC5_Exp_Notes_11Mar2024.xlsx文件")
        return
    
    print(f"正在读取文件: {isic_file}")
    
    # 读取Excel文件的所有sheet
    excel_file = pd.ExcelFile(isic_file)
    print(f"Excel文件包含的sheet: {excel_file.sheet_names}")
    
    # 尝试读取主要的分类数据sheet
    main_sheets = ['ISIC Rev.5', 'Classification', 'Structure', 'Codes']
    data_sheet = None
    
    for sheet in main_sheets:
        if sheet in excel_file.sheet_names:
            data_sheet = sheet
            break
    
    if not data_sheet:
        # 如果没有找到预期的sheet名称，使用第一个sheet
        data_sheet = excel_file.sheet_names[0]
    
    print(f"使用sheet: {data_sheet}")
    
    # 读取数据
    df = pd.read_excel(isic_file, sheet_name=data_sheet)
    print(f"数据形状: {df.shape}")
    print(f"列名: {df.columns.tolist()}")
    print("\n前5行数据:")
    print(df.head())
    
    # 分析数据结构，确定如何映射到目标格式
    # 目标格式: 分类级别,分类代码,分类名称,上级代码,核心关键词,说明
    
    result_data = []
    
    # 根据ISIC代码长度确定分类级别
    def get_classification_level(code):
        if pd.isna(code):
            return None
        code_str = str(code).strip()
        if len(code_str) == 1:
            return "部门"  # Section (A, B, C, etc.)
        elif len(code_str) == 3:
            return "类别"  # Division (A01, A02, etc.)
        elif len(code_str) == 4:
            return "组别"  # Group (A011, A012, etc.)
        elif len(code_str) == 5:
            return "小类"  # Class (A0111, A0112, etc.)
        else:
            return "其他"
    
    def get_parent_code(code):
        if pd.isna(code):
            return ""
        code_str = str(code).strip()
        if len(code_str) <= 1:
            return ""
        else:
            return code_str[:-1]
    
    # 处理数据
    for idx, row in df.iterrows():
        # 尝试找到代码列
        code_col = None
        title_col = None
        desc_col = None
        
        # 常见的列名模式
        for col in df.columns:
            col_lower = str(col).lower()
            if 'code' in col_lower or 'isic' in col_lower:
                if code_col is None:
                    code_col = col
            elif 'title' in col_lower or 'name' in col_lower or 'description' in col_lower:
                if title_col is None:
                    title_col = col
            elif 'explanation' in col_lower or 'note' in col_lower or 'includes' in col_lower:
                if desc_col is None:
                    desc_col = col
        
        # 如果没有找到明确的列名，使用前几列
        if code_col is None and len(df.columns) > 0:
            code_col = df.columns[0]
        if title_col is None and len(df.columns) > 1:
            title_col = df.columns[1]
        if desc_col is None and len(df.columns) > 2:
            desc_col = df.columns[2]
        
        if code_col and title_col:
            code = row[code_col]
            title = row[title_col]
            description = row[desc_col] if desc_col else ""
            
            if pd.notna(code) and pd.notna(title):
                level = get_classification_level(code)
                parent_code = get_parent_code(code)
                
                # 提取关键词（简单处理）
                keywords = str(title).replace(',', ';') if pd.notna(title) else ""
                
                result_data.append({
                    '分类级别': level,
                    '分类代码': str(code).strip(),
                    '分类名称': str(title).strip(),
                    '上级代码': parent_code,
                    '核心关键词': keywords,
                    '说明': str(description).strip() if pd.notna(description) else ""
                })
    
    # 创建结果DataFrame
    result_df = pd.DataFrame(result_data)
    
    # 确保输出目录存在
    output_dir = 'final_data/ISIC_Rev5'
    os.makedirs(output_dir, exist_ok=True)
    
    # 保存结果
    output_file = f'{output_dir}/ISIC_Rev5_converted.csv'
    result_df.to_csv(output_file, index=False, encoding='utf-8-sig')
    
    print(f"\n转换完成！")
    print(f"输出文件: {output_file}")
    print(f"共处理 {len(result_df)} 条记录")
    print(f"\n结果预览:")
    print(result_df.head(10))
    
    return result_df

if __name__ == "__main__":
    extract_isic_rev5_data()
