#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import re

def translate_text(text):
    """
    翻译中文文本到英文
    这里包含了主要的行业分类术语翻译
    """
    if pd.isna(text) or text == '':
        return text
    
    # 定义翻译字典
    translations = {
        # 基础术语
        '分类级别': 'Classification Level',
        '分类代码': 'Classification Code', 
        '分类名称': 'Classification Name',
        '上级代码': 'Parent Code',
        '核心关键词': 'Core Keywords',
        '说明': 'Description',
        
        # 分类级别
        '门类': 'Sector',
        '大类': 'Major Category',
        '中类': 'Medium Category', 
        '小类': 'Minor Category',
        
        # 主要行业门类
        '农、林、牧、渔业': 'Agriculture, Forestry, Animal Husbandry, and Fishery',
        '采矿业': 'Mining Industry',
        '制造业': 'Manufacturing',
        '电力、热力、燃气及水生产和供应业': 'Electricity, Heat, Gas and Water Production and Supply',
        '建筑业': 'Construction',
        '批发和零售业': 'Wholesale and Retail Trade',
        '交通运输、仓储和邮政业': 'Transportation, Storage and Postal Services',
        '住宿和餐饮业': 'Accommodation and Catering',
        '信息传输、软件和信息技术服务业': 'Information Transmission, Software and Information Technology Services',
        '金融业': 'Financial Services',
        '房地产业': 'Real Estate',
        '租赁和商务服务业': 'Leasing and Business Services',
        '科学研究和技术服务业': 'Scientific Research and Technical Services',
        '水利、环境和公共设施管理业': 'Water Conservancy, Environment and Public Facilities Management',
        '居民服务、修理和其他服务业': 'Resident Services, Repair and Other Services',
        '教育': 'Education',
        '卫生和社会工作': 'Health and Social Work',
        '文化、体育和娱乐业': 'Culture, Sports and Entertainment',
        '公共管理、社会保障和社会组织': 'Public Administration, Social Security and Social Organizations',
        '国际组织': 'International Organizations',
        
        # 农业相关
        '农业': 'Agriculture',
        '林业': 'Forestry', 
        '畜牧业': 'Animal Husbandry',
        '渔业': 'Fishery',
        '种植业': 'Planting Industry',
        '养殖业': 'Breeding Industry',
        '谷物种植': 'Grain Cultivation',
        '稻谷种植': 'Rice Cultivation',
        '小麦种植': 'Wheat Cultivation',
        '玉米种植': 'Corn Cultivation',
        '其他谷物种植': 'Other Grain Cultivation',
        '豆类种植': 'Legume Cultivation',
        '油料种植': 'Oil Crop Cultivation',
        '薯类种植': 'Tuber Cultivation',
        '棉花种植': 'Cotton Cultivation',
        '麻类种植': 'Hemp Cultivation',
        '糖料种植': 'Sugar Crop Cultivation',
        '烟草种植': 'Tobacco Cultivation',
        '蔬菜种植': 'Vegetable Cultivation',
        '食用菌种植': 'Edible Fungi Cultivation',
        '花卉种植': 'Flower Cultivation',
        '水果种植': 'Fruit Cultivation',
        '坚果种植': 'Nut Cultivation',
        '茶叶种植': 'Tea Cultivation',
        '中药材种植': 'Traditional Chinese Medicine Material Cultivation',
        '中草药种植': 'Traditional Chinese Herbal Medicine Cultivation',
        '草种植': 'Grass Cultivation',
        '割草': 'Grass Cutting',
        '林木育种': 'Forest Tree Breeding',
        '育苗': 'Seedling Raising',
        '造林': 'Afforestation',
        '更新': 'Renewal',
        '森林经营': 'Forest Management',
        '管护': 'Protection',
        '改培': 'Transformation',
        '木材采运': 'Wood Harvesting and Transportation',
        '竹材采运': 'Bamboo Harvesting and Transportation',
        '林产品采集': 'Forest Product Collection',
        '牲畜饲养': 'Livestock Raising',
        '家禽饲养': 'Poultry Raising',
        '牛的饲养': 'Cattle Raising',
        '马的饲养': 'Horse Raising',
        '猪的饲养': 'Pig Raising',
        '羊的饲养': 'Sheep Raising',
        '骆驼饲养': 'Camel Raising',
        '鸡的饲养': 'Chicken Raising',
        '鸭的饲养': 'Duck Raising',
        '鹅的饲养': 'Goose Raising',
        '兔的饲养': 'Rabbit Raising',
        '蜜蜂饲养': 'Bee Raising',
        '狩猎': 'Hunting',
        '捕捉动物': 'Animal Capturing',
        '水产养殖': 'Aquaculture',
        '海水养殖': 'Marine Aquaculture',
        '内陆养殖': 'Inland Aquaculture',
        '水产捕捞': 'Aquatic Product Fishing',
        '海水捕捞': 'Marine Fishing',
        '内陆捕捞': 'Inland Fishing',
        '动物饲养': 'Animal Raising',
        
        # 采矿业相关
        '开采': 'Mining',
        '矿业': 'Mining Industry',
        '资源开采': 'Resource Extraction',
        '煤炭开采': 'Coal Mining',
        '洗选业': 'Washing Industry',
        '烟煤': 'Bituminous Coal',
        '无烟煤': 'Anthracite Coal',
        '褐煤': 'Lignite',
        '石油开采': 'Oil Extraction',
        '天然气开采': 'Natural Gas Extraction',
        '陆地石油开采': 'Onshore Oil Extraction',
        '海洋石油开采': 'Offshore Oil Extraction',
        '陆地天然气开采': 'Onshore Natural Gas Extraction',
        '海洋天然气': 'Offshore Natural Gas',
        '可燃冰开采': 'Gas Hydrate Extraction',
        '黑色金属矿采选业': 'Ferrous Metal Ore Mining and Beneficiation',
        '有色金属矿采选业': 'Non-ferrous Metal Ore Mining and Beneficiation',
        '非金属矿采选业': 'Non-metallic Mineral Mining and Beneficiation',
        '铁矿采选': 'Iron Ore Mining and Beneficiation',
        '锰矿': 'Manganese Ore',
        '铬矿采选': 'Chrome Ore Mining and Beneficiation',
        '铜矿采选': 'Copper Ore Mining and Beneficiation',
        '铅锌矿采选': 'Lead-Zinc Ore Mining and Beneficiation',
        '镍钴矿采选': 'Nickel-Cobalt Ore Mining and Beneficiation',
        '锡矿采选': 'Tin Ore Mining and Beneficiation',
        '锑矿采选': 'Antimony Ore Mining and Beneficiation',
        '铝矿采选': 'Aluminum Ore Mining and Beneficiation',
        '镁矿采选': 'Magnesium Ore Mining and Beneficiation',
        '金矿采选': 'Gold Ore Mining and Beneficiation',
        '银矿采选': 'Silver Ore Mining and Beneficiation',
        '钨钼矿采选': 'Tungsten-Molybdenum Ore Mining and Beneficiation',
        '稀土金属矿采选': 'Rare Earth Metal Ore Mining and Beneficiation',
        '放射性金属矿采选': 'Radioactive Metal Ore Mining and Beneficiation',
        '土砂石开采': 'Earth and Stone Mining',
        '石灰石': 'Limestone',
        '石膏开采': 'Gypsum Mining',
        '化学矿开采': 'Chemical Mineral Mining',
        '采盐': 'Salt Mining',
        '石棉': 'Asbestos',
        '云母矿采选': 'Mica Mining and Beneficiation',
        '石墨': 'Graphite',
        '滑石采选': 'Talc Mining and Beneficiation',
        '宝石': 'Gemstone',
        '玉石采选': 'Jade Mining and Beneficiation',
        
        # 制造业相关
        '制造加工': 'Manufacturing and Processing',
        '生产制造': 'Production Manufacturing',
        '工业生产': 'Industrial Production',
        '加工业': 'Processing Industry',
        '制造企业': 'Manufacturing Enterprises',
        '生产加工': 'Production Processing',
        '制造': 'Manufacturing',
        '工业制造': 'Industrial Manufacturing',
        '农副食品加工业': 'Agricultural and Food Processing',
        '食品制造业': 'Food Manufacturing',
        '谷物磨制': 'Grain Milling',
        '饲料加工': 'Feed Processing',
        '植物油加工': 'Vegetable Oil Processing',
        '制糖业': 'Sugar Manufacturing',
        '屠宰': 'Slaughtering',
        '肉类加工': 'Meat Processing',
        '水产品加工': 'Aquatic Product Processing',
        '稻谷加工': 'Rice Processing',
        '小麦加工': 'Wheat Processing',
        '玉米加工': 'Corn Processing',
        '杂粮加工': 'Miscellaneous Grain Processing',
        '宠物饲料加工': 'Pet Feed Processing',
        '食用植物油加工': 'Edible Vegetable Oil Processing',
        '非食用植物油加工': 'Non-edible Vegetable Oil Processing',
        '牲畜屠宰': 'Livestock Slaughtering',
        '禽类屠宰': 'Poultry Slaughtering',
        '肉制品': 'Meat Products',
        '副产品加工': 'By-product Processing',
        '水产品冷冻加工': 'Aquatic Product Freezing Processing',
        '鱼糜制品': 'Fish Paste Products',
        '鱼油提取': 'Fish Oil Extraction',
        '制品制造': 'Product Manufacturing',
        '蔬菜加工': 'Vegetable Processing',
        '食用菌加工': 'Edible Fungi Processing',
        '坚果加工': 'Nut Processing',
        '淀粉': 'Starch',
        '淀粉制品制造': 'Starch Product Manufacturing',
        '豆制品制造': 'Bean Product Manufacturing',
        '蛋品加工': 'Egg Product Processing',
        '焙烤食品制造': 'Baked Food Manufacturing',
        '糕点': 'Cake',
        '面包制造': 'Bread Manufacturing',
        '饼干': 'Biscuit',
        '糖果': 'Candy',
        '巧克力': 'Chocolate',
        '蜜饯': 'Preserved Fruit',
        '方便食品制造': 'Convenience Food Manufacturing',
        '米': 'Rice',
        '面制品制造': 'Flour Product Manufacturing',
        '速冻食品制造': 'Frozen Food Manufacturing',
        '方便面制造': 'Instant Noodle Manufacturing',
        '乳制品制造': 'Dairy Product Manufacturing',
        
        # 建筑业相关
        '建筑': 'Construction',
        '工程': 'Engineering',
        '施工': 'Construction',
        '建筑装饰': 'Building Decoration',
        
        # 通用术语
        '专业': 'Professional',
        '辅助性活动': 'Support Activities',
        '其他': 'Other',
        '未列明': 'Unlisted',
        '常用': 'Common',
        '贵金属': 'Precious Metal',
        '稀有': 'Rare',
        '稀土': 'Rare Earth',
        '放射性': 'Radioactive',
        '天然': 'Natural',
        '人工': 'Artificial',
        '机械': 'Machinery',
        '设备': 'Equipment',
        '活动': 'Activities',
        '生产': 'Production',
        '经营': 'Operation',
        '管理': 'Management',
        '服务': 'Service',
        '技术': 'Technology',
        '科学': 'Science',
        '研究': 'Research',
        '开发': 'Development',
        '设计': 'Design',
        '咨询': 'Consulting',
        '培训': 'Training',
        '教育': 'Education',
        '卫生': 'Health',
        '医疗': 'Medical',
        '社会': 'Social',
        '公共': 'Public',
        '文化': 'Culture',
        '体育': 'Sports',
        '娱乐': 'Entertainment',
        '旅游': 'Tourism',
        '交通': 'Transportation',
        '运输': 'Transport',
        '仓储': 'Storage',
        '物流': 'Logistics',
        '邮政': 'Postal',
        '通信': 'Communication',
        '信息': 'Information',
        '软件': 'Software',
        '网络': 'Network',
        '金融': 'Finance',
        '保险': 'Insurance',
        '银行': 'Banking',
        '证券': 'Securities',
        '房地产': 'Real Estate',
        '租赁': 'Leasing',
        '商务': 'Business',
        '批发': 'Wholesale',
        '零售': 'Retail',
        '贸易': 'Trade',
        '住宿': 'Accommodation',
        '餐饮': 'Catering',
        '环境': 'Environment',
        '环保': 'Environmental Protection',
        '能源': 'Energy',
        '电力': 'Electricity',
        '热力': 'Heat',
        '燃气': 'Gas',
        '水': 'Water',
        '供应': 'Supply',
    }
    
    # 处理分号分隔的关键词
    if ';' in str(text):
        keywords = str(text).split(';')
        translated_keywords = []
        for keyword in keywords:
            keyword = keyword.strip()
            translated = translations.get(keyword, keyword)
            translated_keywords.append(translated)
        return ';'.join(translated_keywords)
    
    # 处理单个词汇或短语
    text_str = str(text).strip()
    
    # 直接匹配
    if text_str in translations:
        return translations[text_str]
    
    # 部分匹配替换
    result = text_str
    for chinese, english in translations.items():
        if chinese in result:
            result = result.replace(chinese, english)
    
    return result

def translate_csv_file(input_file, output_file):
    """
    翻译CSV文件
    """
    print(f"正在读取文件: {input_file}")
    
    # 读取CSV文件
    try:
        df = pd.read_csv(input_file, encoding='utf-8')
    except UnicodeDecodeError:
        try:
            df = pd.read_csv(input_file, encoding='gbk')
        except UnicodeDecodeError:
            df = pd.read_csv(input_file, encoding='gb2312')
    
    print(f"文件包含 {len(df)} 行数据")
    
    # 翻译每一列
    for column in df.columns:
        print(f"正在翻译列: {column}")
        df[column] = df[column].apply(translate_text)
    
    # 保存翻译后的文件
    print(f"正在保存翻译后的文件: {output_file}")
    df.to_csv(output_file, index=False, encoding='utf-8')
    
    print(f"翻译完成！共处理了 {len(df)} 行数据")

if __name__ == "__main__":
    input_file = "GB_T_4754_2017_converted.csv"
    output_file = "GB_T_4754_2017_converted_english.csv"
    
    translate_csv_file(input_file, output_file)