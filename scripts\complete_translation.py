#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import re

def complete_translation():
    """
    完全重新翻译文件
    """
    print("开始完全重新翻译...")
    
    # 读取原始中文文件
    df = pd.read_csv("GB_T_4754_2017_converted.csv", encoding='utf-8')
    
    # 创建新的DataFrame，手动翻译表头
    result_df = pd.DataFrame()
    result_df['Classification Level'] = df.iloc[:, 0].apply(translate_classification_level)
    result_df['Classification Code'] = df.iloc[:, 1]  # 代码保持不变
    result_df['Classification Name'] = df.iloc[:, 2].apply(translate_name)
    result_df['Parent Code'] = df.iloc[:, 3]  # 代码保持不变
    result_df['Core Keywords'] = df.iloc[:, 4].apply(translate_keywords)
    result_df['Description'] = df.iloc[:, 5].apply(translate_description)
    
    # 保存结果
    result_df.to_csv("GB_T_4754_2017_converted_english_complete.csv", index=False, encoding='utf-8')
    print(f"翻译完成！共处理 {len(result_df)} 行数据")

def translate_classification_level(text):
    """翻译分类级别"""
    if pd.isna(text):
        return text
    
    translations = {
        '分类级别': 'Classification Level',
        '门类': 'Sector',
        '大类': 'Major Category', 
        '中类': 'Medium Category',
        '小类': 'Minor Category'
    }
    
    text_str = str(text)
    for cn, en in translations.items():
        if cn in text_str:
            return en
    return text_str

def translate_name(text):
    """翻译分类名称"""
    if pd.isna(text):
        return text
    
    # 主要行业名称翻译
    names = {
        '分类名称': 'Classification Name',
        '农、林、牧、渔业': 'Agriculture, Forestry, Animal Husbandry and Fishery',
        '采矿业': 'Mining',
        '制造业': 'Manufacturing',
        '电力、热力、燃气及水生产和供应业': 'Electricity, Heat, Gas and Water Production and Supply',
        '建筑业': 'Construction',
        '批发和零售业': 'Wholesale and Retail Trade',
        '交通运输、仓储和邮政业': 'Transportation, Storage and Postal Services',
        '住宿和餐饮业': 'Accommodation and Food Services',
        '信息传输、软件和信息技术服务业': 'Information Transmission, Software and Information Technology Services',
        '金融业': 'Financial Intermediation',
        '房地产业': 'Real Estate Activities',
        '租赁和商务服务业': 'Renting and Business Activities',
        '科学研究和技术服务业': 'Scientific Research and Technical Services',
        '水利、环境和公共设施管理业': 'Water Conservancy, Environment and Public Facilities Management',
        '居民服务、修理和其他服务业': 'Resident Services, Repair and Other Services',
        '教育': 'Education',
        '卫生和社会工作': 'Health and Social Work',
        '文化、体育和娱乐业': 'Culture, Sports and Entertainment',
        '公共管理、社会保障和社会组织': 'Public Administration, Social Security and Social Organizations',
        '国际组织': 'International Organizations',
        
        # 农业细分
        '农业': 'Agriculture',
        '林业': 'Forestry',
        '畜牧业': 'Animal Husbandry', 
        '渔业': 'Fishery',
        '农、林、牧、渔专业及辅助性活动': 'Professional and Support Activities for Agriculture, Forestry, Animal Husbandry and Fishery',
        '谷物种植': 'Grain Cultivation',
        '稻谷种植': 'Rice Cultivation',
        '小麦种植': 'Wheat Cultivation',
        '玉米种植': 'Corn Cultivation',
        '其他谷物种植': 'Other Grain Cultivation',
        '豆类、油料和薯类种植': 'Legume, Oil Crop and Tuber Cultivation',
        '豆类种植': 'Legume Cultivation',
        '油料种植': 'Oil Crop Cultivation',
        '薯类种植': 'Tuber Cultivation',
        '棉、麻、糖、烟草种植': 'Cotton, Hemp, Sugar and Tobacco Cultivation',
        '棉花种植': 'Cotton Cultivation',
        '麻类种植': 'Hemp Cultivation',
        '糖料种植': 'Sugar Crop Cultivation',
        '烟草种植': 'Tobacco Cultivation',
        '蔬菜、食用菌及园艺作物种植': 'Vegetable, Edible Fungi and Horticultural Crop Cultivation',
        '蔬菜种植': 'Vegetable Cultivation',
        '食用菌种植': 'Edible Fungi Cultivation',
        '花卉种植': 'Flower Cultivation',
        '其他园艺作物种植': 'Other Horticultural Crop Cultivation',
        '水果种植': 'Fruit Cultivation',
        '仁果类和核果类水果种植': 'Pome and Stone Fruit Cultivation',
        '葡萄种植': 'Grape Cultivation',
        '柑橘类种植': 'Citrus Cultivation',
        '香蕉等亚热带水果种植': 'Banana and Other Subtropical Fruit Cultivation',
        '其他水果种植': 'Other Fruit Cultivation',
        '坚果、含油果、香料和饮料作物种植': 'Nut, Oil-bearing Fruit, Spice and Beverage Crop Cultivation',
        '坚果种植': 'Nut Cultivation',
        '含油果种植': 'Oil-bearing Fruit Cultivation',
        '香料作物种植': 'Spice Crop Cultivation',
        '茶叶种植': 'Tea Cultivation',
        '其他饮料作物种植': 'Other Beverage Crop Cultivation',
        '中药材种植': 'Traditional Chinese Medicine Material Cultivation',
        '中草药种植': 'Traditional Chinese Herbal Medicine Cultivation',
        '其他中药材种植': 'Other Traditional Chinese Medicine Material Cultivation',
        '草种植及割草': 'Grass Cultivation and Cutting',
        '草种植': 'Grass Cultivation',
        '天然草原割草': 'Natural Grassland Cutting',
        '其他农业': 'Other Agriculture',
        
        # 林业
        '林木育种和育苗': 'Forest Tree Breeding and Seedling Raising',
        '林木育种': 'Forest Tree Breeding',
        '林木育苗': 'Forest Tree Seedling Raising',
        '造林和更新': 'Afforestation and Renewal',
        '森林经营、管护和改培': 'Forest Management, Protection and Transformation',
        '森林经营和管护': 'Forest Management and Protection',
        '森林改培': 'Forest Transformation',
        '木材和竹材采运': 'Wood and Bamboo Harvesting and Transportation',
        '木材采运': 'Wood Harvesting and Transportation',
        '竹材采运': 'Bamboo Harvesting and Transportation',
        '林产品采集': 'Forest Product Collection',
        '木竹材林产品采集': 'Wood and Bamboo Forest Product Collection',
        '非木竹材林产品采集': 'Non-wood and Non-bamboo Forest Product Collection',
        
        # 畜牧业
        '牲畜饲养': 'Livestock Raising',
        '牛的饲养': 'Cattle Raising',
        '马的饲养': 'Horse Raising',
        '猪的饲养': 'Pig Raising',
        '羊的饲养': 'Sheep Raising',
        '骆驼饲养': 'Camel Raising',
        '其他牲畜饲养': 'Other Livestock Raising',
        '家禽饲养': 'Poultry Raising',
        '鸡的饲养': 'Chicken Raising',
        '鸭的饲养': 'Duck Raising',
        '鹅的饲养': 'Goose Raising',
        '其他家禽饲养': 'Other Poultry Raising',
        '狩猎和捕捉动物': 'Hunting and Animal Capturing',
        '其他畜牧业': 'Other Animal Husbandry',
        '兔的饲养': 'Rabbit Raising',
        '蜜蜂饲养': 'Bee Raising',
        '其他未列明畜牧业': 'Other Animal Husbandry Not Elsewhere Classified',
        
        # 渔业
        '水产养殖': 'Aquaculture',
        '海水养殖': 'Marine Aquaculture',
        '内陆养殖': 'Inland Aquaculture',
        '水产捕捞': 'Fishing',
        '海水捕捞': 'Marine Fishing',
        '内陆捕捞': 'Inland Fishing',
        
        # 专业及辅助性活动
        '农业专业及辅助性活动': 'Professional and Support Activities for Agriculture',
        '种子种苗培育活动': 'Seed and Seedling Cultivation Activities',
        '农业机械活动': 'Agricultural Machinery Activities',
        '灌溉活动': 'Irrigation Activities',
        '农产品初加工活动': 'Primary Processing Activities for Agricultural Products',
        '农作物病虫害防治活动': 'Crop Pest and Disease Control Activities',
        '其他农业专业及辅助性活动': 'Other Professional and Support Activities for Agriculture',
        '林业专业及辅助性活动': 'Professional and Support Activities for Forestry',
        '林业有害生物防治活动': 'Forestry Pest Control Activities',
        '森林防火活动': 'Forest Fire Prevention Activities',
        '林产品初级加工活动': 'Primary Processing Activities for Forest Products',
        '其他林业专业及辅助性活动': 'Other Professional and Support Activities for Forestry',
        '畜牧专业及辅助性活动': 'Professional and Support Activities for Animal Husbandry',
        '畜牧良种繁殖活动': 'Livestock Breeding Activities',
        '畜禽粪污处理活动': 'Livestock and Poultry Manure Treatment Activities',
        '其他畜牧专业及辅助性活动': 'Other Professional and Support Activities for Animal Husbandry',
        '渔业专业及辅助性活动': 'Professional and Support Activities for Fishery',
        '鱼苗及鱼种场活动': 'Fish Fry and Fish Seed Farm Activities',
        '其他渔业专业及辅助性活动': 'Other Professional and Support Activities for Fishery',
        
        # 其他常见行业
        '公共管理和社会组织': 'Public Administration and Social Organizations',
        '国家机构': 'State Institutions',
        '人大和政协': 'People\'s Congress and Political Consultative Conference',
        '国家行政机构': 'State Administrative Institutions',
        '人民法院和人民检察院': 'People\'s Courts and People\'s Procuratorates',
        '中国共产党机关': 'Communist Party of China Organizations',
        '民主党派': 'Democratic Parties',
        '社会保障': 'Social Security',
        '群众团体、社会团体和其他成员组织': 'Mass Organizations, Social Organizations and Other Member Organizations',
        '群众团体': 'Mass Organizations',
        '工会': 'Trade Unions',
        '妇联': 'Women\'s Federations',
        '共青团': 'Communist Youth League',
        '社会团体': 'Social Organizations',
        '专业性团体': 'Professional Organizations',
        '行业性团体': 'Industry Organizations',
        '宗教组织': 'Religious Organizations',
        '宗教团体服务': 'Religious Organization Services',
        '宗教活动场所服务': 'Religious Activity Venue Services',
        '基层群众自治组织和其他组织': 'Grassroots Mass Autonomous Organizations and Other Organizations',
        '社区居民自治组织': 'Community Residents\' Autonomous Organizations',
        '村民自治组织': 'Villagers\' Autonomous Organizations',
        '国际组织': 'International Organizations',
    }
    
    text_str = str(text)
    for cn, en in sorted(names.items(), key=lambda x: len(x[0]), reverse=True):
        if cn in text_str:
            text_str = text_str.replace(cn, en)
    
    return text_str

def translate_keywords(text):
    """翻译核心关键词"""
    if pd.isna(text):
        return text
    
    keywords = {
        '核心关键词': 'Core Keywords',
        '农业生产': 'agricultural production',
        '种植业': 'planting industry',
        '畜牧业': 'animal husbandry',
        '林业': 'forestry',
        '渔业': 'fishery',
        '农产品': 'agricultural products',
        '农业': 'agriculture',
        '农作物': 'crops',
        '作物生产': 'crop production',
        '谷物种植': 'grain cultivation',
        '豆类': 'legumes',
        '油料': 'oil crops',
        '薯类种植': 'tuber cultivation',
        '棉': 'cotton',
        '麻': 'hemp',
        '糖': 'sugar',
        '烟草种植': 'tobacco cultivation',
        '蔬菜': 'vegetables',
        '食用菌': 'edible fungi',
        '园艺作物种植': 'horticultural crop cultivation',
        '水果种植': 'fruit cultivation',
        '仁果类': 'pome fruits',
        '核果类水果种植': 'stone fruit cultivation',
        '葡萄种植': 'grape cultivation',
        '柑橘类种植': 'citrus cultivation',
        '香蕉等亚热带水果种植': 'banana and other subtropical fruit cultivation',
        '其他水果种植': 'other fruit cultivation',
        '坚果': 'nuts',
        '含油果': 'oil-bearing fruits',
        '香料和饮料作物种植': 'spice and beverage crop cultivation',
        '茶叶种植': 'tea cultivation',
        '其他饮料作物种植': 'other beverage crop cultivation',
        '中药材种植': 'traditional Chinese medicine material cultivation',
        '中草药种植': 'traditional Chinese herbal medicine cultivation',
        '其他中药材种植': 'other traditional Chinese medicine material cultivation',
        '草种植': 'grass cultivation',
        '割草': 'grass cutting',
        '天然草原割草': 'natural grassland cutting',
        '其他农业': 'other agriculture',
        '林木育种': 'forest tree breeding',
        '育苗': 'seedling raising',
        '造林': 'afforestation',
        '更新': 'renewal',
        '森林经营': 'forest management',
        '管护和改培': 'protection and transformation',
        '木材': 'wood',
        '竹材采运': 'bamboo harvesting and transportation',
        '林产品采集': 'forest product collection',
        '养殖业': 'breeding industry',
        '动物饲养': 'animal raising',
        '牲畜饲养': 'livestock raising',
        '牛的饲养': 'cattle raising',
        '马的饲养': 'horse raising',
        '猪的饲养': 'pig raising',
        '羊的饲养': 'sheep raising',
        '骆驼饲养': 'camel raising',
        '其他牲畜饲养': 'other livestock raising',
        '家禽饲养': 'poultry raising',
        '鸡的饲养': 'chicken raising',
        '鸭的饲养': 'duck raising',
        '鹅的饲养': 'goose raising',
        '其他家禽饲养': 'other poultry raising',
        '狩猎': 'hunting',
        '捕捉动物': 'animal capturing',
        '其他畜牧业': 'other animal husbandry',
        '兔的饲养': 'rabbit raising',
        '蜜蜂饲养': 'bee raising',
        '其他未列明畜牧业': 'other animal husbandry not elsewhere classified',
        '水产养殖': 'aquaculture',
        '海水养殖': 'marine aquaculture',
        '内陆养殖': 'inland aquaculture',
        '水产捕捞': 'fishing',
        '海水捕捞': 'marine fishing',
        '内陆捕捞': 'inland fishing',
        '农': 'agriculture',
        '林': 'forestry',
        '牧': 'animal husbandry',
        '渔专业及辅助性活动': 'fishery professional and support activities',
        '农业专业': 'agricultural professional',
        '辅助性活动': 'support activities',
        '种子种苗培育活动': 'seed and seedling cultivation activities',
        '农业机械活动': 'agricultural machinery activities',
        '灌溉活动': 'irrigation activities',
        '加工业': 'processing industry',
        '生产加工': 'production processing',
        '制造': 'manufacturing',
        '农产品初加工活动': 'primary processing activities for agricultural products',
        '农作物病虫害防治活动': 'crop pest and disease control activities',
        '其他农业专业': 'other agricultural professional',
        '林业专业': 'forestry professional',
        '林业有害生物防治活动': 'forestry pest control activities',
        '森林防火活动': 'forest fire prevention activities',
        '林产品初级加工活动': 'primary processing activities for forest products',
        '其他林业专业': 'other forestry professional',
        '畜牧专业': 'animal husbandry professional',
        '畜牧良种繁殖活动': 'livestock breeding activities',
        '畜禽粪污处理活动': 'livestock and poultry manure treatment activities',
        '其他畜牧专业': 'other animal husbandry professional',
        '渔业专业': 'fishery professional',
        '鱼苗': 'fish fry',
        '鱼种场活动': 'fish seed farm activities',
        '其他渔业专业': 'other fishery professional',
        
        # 其他领域关键词
        '国际组织': 'international organizations',
        '国际合作': 'international cooperation',
        '宗教组织': 'religious organizations',
        '宗教团体服务': 'religious organization services',
        '宗教活动场所服务': 'religious activity venue services',
        '基层群众自治组织': 'grassroots mass autonomous organizations',
        '其他组织': 'other organizations',
        '社区居民自治组织': 'community residents\' autonomous organizations',
        '村民自治组织': 'villagers\' autonomous organizations',
        '服务业': 'service industry',
        '商业服务': 'commercial services',
    }
    
    # 处理分号分隔的关键词
    if ';' in str(text):
        keyword_list = str(text).split(';')
        translated_keywords = []
        for keyword in keyword_list:
            keyword = keyword.strip()
            translated = keyword
            for cn, en in sorted(keywords.items(), key=lambda x: len(x[0]), reverse=True):
                if cn in translated:
                    translated = translated.replace(cn, en)
            translated_keywords.append(translated)
        return ';'.join(translated_keywords)
    
    # 处理单个关键词
    text_str = str(text)
    for cn, en in sorted(keywords.items(), key=lambda x: len(x[0]), reverse=True):
        if cn in text_str:
            text_str = text_str.replace(cn, en)
    
    return text_str

def translate_description(text):
    """翻译说明文字"""
    if pd.isna(text):
        return text
    
    descriptions = {
        '说明': 'Description',
        '本门类包括': 'This sector includes',
        '本类包括': 'This category includes',
        '指对各种农作物的种植': 'Refers to the cultivation of various crops',
        '指以收获籽实为主的农作物的种植，包括稻谷、小麦、玉米等农作物的种植和作为饲料和工业原料的谷物的种植': 'Refers to the cultivation of crops mainly for harvesting seeds, including the cultivation of rice, wheat, corn and other crops, and the cultivation of grains used as feed and industrial raw materials',
        '指用于制糖的甘蔗和甜菜的种植': 'Refers to the cultivation of sugarcane and sugar beet for sugar production',
        '指苹果、梨、桃、杏、李子等水果种植': 'Refers to the cultivation of fruits such as apples, pears, peaches, apricots, and plums',
        '指香蕉、菠萝、芒果等亚热带水果种植': 'Refers to the cultivation of subtropical fruits such as bananas, pineapples, and mangoes',
        '指油茶、橄榄、油棕榈、油桐籽、椰子等种植': 'Refers to the cultivation of oil tea, olives, oil palms, tung seeds, coconuts, etc.',
        '指主要用于中药配制以及中成药加工的药材作物的种植': 'Refers to the cultivation of medicinal crops mainly used for traditional Chinese medicine preparation and traditional Chinese medicine processing',
        '指主要用于中药配制以及中成药加工的各种中草药材作物的种植': 'Refers to the cultivation of various traditional Chinese herbal medicine crops mainly used for traditional Chinese medicine preparation and traditional Chinese medicine processing',
        '指人工种植收获牧草': 'Refers to artificial cultivation and harvesting of pasture grass',
        '指天然草原刈割收获牧草': 'Refers to cutting and harvesting pasture grass from natural grasslands',
        '指应用遗传学原理选育、繁殖林木良种和繁殖林木新品种核心的栽植材料的林木遗传改良活动': 'Refers to forest genetic improvement activities that apply genetic principles to select and breed improved forest tree varieties and reproduce core planting materials for new forest tree varieties',
        '指通过人为活动将种子、穗条或植物其他组织培育成苗木的活动': 'Refers to activities of cultivating seeds, shoots or other plant tissues into seedlings through human activities',
        '指在宜林荒山荒地荒沙、采伐迹地、火烧迹地、疏林地、灌木林地等一切可造林的土地上通过人工造林、人工更新、封山育林、飞播造林等方式培育和恢复森林的活动': 'Refers to activities of cultivating and restoring forests through artificial afforestation, artificial renewal, mountain closure for forest cultivation, aerial seeding afforestation and other methods on all forestable lands such as suitable barren mountains, wastelands, sandy lands, cut-over lands, burned lands, sparse forest lands, and shrub lands',
        '指为促进林木生长发育，在林木生长的不同时期进行的促进林木生长发育的活动': 'Refers to activities conducted during different periods of tree growth to promote tree growth and development',
        '指为调整林分结构和树种组成，形成密度合理、物种丰富、功能完备的优质、高产、高效林而采取林分抚育、补植、补播等人工措施的活动': 'Refers to activities that take artificial measures such as forest stand tending, replanting, and resowing to adjust forest stand structure and tree species composition to form high-quality, high-yield, and efficient forests with reasonable density, rich species, and complete functions',
        '指对林木和竹木的采伐，并将其运出山场至贮木场的生产活动': 'Refers to production activities of harvesting trees and bamboo and transporting them from mountain sites to storage yards',
        '指在天然林地和人工林地进行的各种林木产品和其他野生植物的采集等活动': 'Refers to activities of collecting various forest products and other wild plants in natural forests and artificial forests',
        '指在天然林地和人工林地进行的除木材、竹材产品外的其他各种林产品的采集活动': 'Refers to activities of collecting various forest products other than wood and bamboo products in natural forests and artificial forests',
        '指为了获得各种畜禽产品而从事的动物饲养、捕捉活动': 'Refers to animal raising and capturing activities for obtaining various livestock and poultry products',
        '指对各种野生动物的捕捉以及与此相关的活动': 'Refers to the capture of various wild animals and related activities',
        '指利用海水对各种水生动植物的养殖': 'Refers to the cultivation of various aquatic animals and plants using seawater',
        '指在内陆水域进行的各种水生动植物的养殖': 'Refers to the cultivation of various aquatic animals and plants in inland waters',
        '指在海洋中对各种天然水生动植物的捕捞': 'Refers to the fishing of various natural aquatic animals and plants in the ocean',
        '指在内陆水域对各种天然水生动植物的捕捞': 'Refers to the fishing of various natural aquatic animals and plants in inland waters',
        '指对农业提供的各种专业及辅助性生产活动，不包括各种科学技术和专业技术服务': 'Refers to various professional and auxiliary production activities provided for agriculture, excluding various scientific and technical services',
        '指为农业生产提供农业机械并配备操作人员的活动': 'Refers to activities that provide agricultural machinery and equipped operators for agricultural production',
        '指对农业生产灌溉排水系统的经营与管理': 'Refers to the operation and management of irrigation and drainage systems for agricultural production',
        '指对各种农产品（包括天然橡胶、纺织纤维原料）进行脱水、凝固、打蜡、去籽、净化、分类、晒干、剥皮、初烤、沤软或大批包装以提供初级市场的服务，以及其他农产品的初加工；其中棉花等纺织纤维原料加工指对棉纤维、短绒剥离后的棉籽以及棉花秸秆、铃壳等副产品的综合加工和利用活动': 'Refers to services of dehydrating, solidifying, waxing, deseeding, purifying, classifying, sun drying, peeling, primary baking, retting or bulk packaging various agricultural products (including natural rubber and textile fiber raw materials) to provide primary markets, as well as other primary processing of agricultural products; among them, cotton and other textile fiber raw material processing refers to comprehensive processing and utilization activities of cotton fiber, short lint stripped cotton seeds, and cotton straw, boll shells and other by-products',
        '指从事农作物重大病虫害防治等活动': 'Refers to activities engaged in the prevention and control of major crop pests and diseases',
        '指代耕代种代收、大田托管等其他农业活动': 'Refers to other agricultural activities such as contract farming, contract planting, contract harvesting, and field trusteeship',
        '指为林业生产提供的林业有害生物防治、林地防火等各种辅助性活动': 'Refers to various auxiliary activities provided for forestry production such as forestry pest control and forest land fire prevention',
        '指对各种林产品进行去皮、打枝或去料、净化、初包装提供至贮木场或初级加工活动': 'Refers to activities of debarking, pruning or debris removal, purification, and primary packaging of various forest products provided to storage yards or primary processing',
        '指提供牲畜繁殖、圈舍清理、畜产品生产、初级加工、动物免疫接种、标识佩戴和动物诊疗等活动': 'Refers to activities of providing livestock reproduction, pen cleaning, livestock product production, primary processing, animal immunization, identification wearing, and animal diagnosis and treatment',
        '指对渔业生产提供的各种活动，包括鱼苗及鱼种场、水产良种场和水产增殖场等活动': 'Refers to various activities provided for fishery production, including fish fry and fish seed farms, aquatic breeding farms, and aquatic enhancement farms',
        
        # 公共管理和社会组织相关
        '指通过选举产生的社区性组织，该组织为本地区提供一般性管理、调解、治安、优抚、计划生育等服务': 'Refers to community organizations elected through elections that provide general management, mediation, public security, preferential treatment, family planning and other services for the local area',
        '指城市、镇居民通过选举产生的群众性自治组织管理活动': 'Refers to management activities of mass autonomous organizations elected by urban and town residents',
        '指农村村民通过选举产生的群众性自治组织管理活动': 'Refers to management activities of mass autonomous organizations elected by rural villagers',
        '指联合国及其他国际组织驻我国境内机构及其他活动': 'Refers to institutions of the United Nations and other international organizations in China and other activities',
        '指民政部门登记的宗教团体活动及在政府宗教事务部门登记的宗教活动场所的活动': 'Refers to activities of religious organizations registered with civil affairs departments and activities of religious activity venues registered with government religious affairs departments',
        
        # 常见短语
        '指对': 'Refers to',
        '指以': 'Refers to',
        '指用于': 'Refers to',
        '指在': 'Refers to',
        '指为': 'Refers to',
        '指从事': 'Refers to engaging in',
        '指应用': 'Refers to applying',
        '指通过': 'Refers to through',
        '指利用': 'Refers to using',
        '指主要': 'Refers to mainly',
        '指将': 'Refers to converting',
        '指把': 'Refers to converting',
        '指各种': 'Refers to various',
        '各种': 'various',
        '以及': 'and',
        '包括': 'including',
        '等': 'etc.',
        '等活动': 'and other activities',
        '等生产活动': 'and other production activities',
        '的生产活动': 'production activities',
        '的活动': 'activities',
        '的种植': 'cultivation',
        '的开采': 'mining',
        '的加工': 'processing',
        '的制造': 'manufacturing',
        '的采集': 'collection',
        '的饲养': 'raising',
        '的养殖': 'breeding',
        '的捕捞': 'fishing',
        '的采运': 'harvesting and transportation',
        '不包括': 'does not include',
        '不含': 'does not include',
        '还包括': 'also includes',
        '也包括': 'also includes',
        '以及其他': 'and other',
        '其他': 'other',
        '未列明': 'not listed elsewhere',
        '未列明的': 'not listed elsewhere',
        '其他未列明': 'other not listed elsewhere',
        '一般': 'general',
        '主要': 'main',
        '专门': 'specialized',
        '天然': 'natural',
        '人工': 'artificial',
        '野生': 'wild',
        '为了': 'in order to',
        '而从事': 'engaged in',
        '获得': 'obtain',
        '进行': 'conduct',
        '采取': 'take',
        '提供': 'provide',
        '经过': 'through',
        '通过': 'through',
        '利用': 'using',
        '使用': 'using',
        '应用': 'applying',
        '采用': 'adopting',
        '配备': 'equipped with',
        '配以': 'supplemented with',
        '辅以': 'assisted by',
        '辅料': 'auxiliary materials',
        '原料': 'raw materials',
        '材料': 'materials',
        '成型': 'molding',
        '油炸': 'frying',
        '烤制': 'baking',
        '而成': 'made from',
        '制成': 'made into',
        '加工制成': 'processed into',
        '经加工': 'after processing',
        '经': 'after',
        '后': 'after',
        '时': 'when',
        '中': 'in',
        '上': 'on',
        '下': 'under',
        '内': 'inside',
        '外': 'outside',
        '间': 'between',
        '与': 'and',
        '及': 'and',
        '或': 'or',
        '但': 'but',
        '除': 'except',
        '由': 'by',
        '从': 'from',
        '至': 'to',
        '到': 'to',
        '向': 'to',
        '对': 'to',
        '为': 'for',
        '用': 'use',
        '作': 'as',
        '成': 'into',
        '于': 'at',
        '在': 'in',
        '将': 'will',
        '把': 'to',
        '使': 'make',
        '让': 'let',
        '的': '',
        '了': '',
        '着': '',
        '过': '',
        '得': '',
        '地': '',
        '种植': 'cultivation',
        '栽培': 'cultivation',
        '培育': 'cultivation',
        '培养': 'cultivation',
        '育种': 'breeding',
        '育苗': 'seedling raising',
        '繁殖': 'reproduction',
        '繁育': 'breeding',
        '苗木': 'seedlings',
        '种子': 'seeds',
        '种苗': 'seedlings',
        '良种': 'improved varieties',
        '品种': 'varieties',
        '新品种': 'new varieties',
        '遗传': 'genetic',
        '遗传学': 'genetics',
        '原理': 'principles',
        '选育': 'breeding',
        '栽植': 'planting',
        '改良': 'improvement',
        '穗条': 'shoots',
        '植物': 'plants',
        '组织': 'tissues',
        '造林': 'afforestation',
        '更新': 'renewal',
        '活动': 'activities',
        '生产': 'production',
        '生产活动': 'production activities',
        '经营': 'operation',
        '管理': 'management',
        '服务': 'service',
        '技术': 'technology',
        '科学': 'science',
        '研究': 'research',
        '开发': 'development',
        '设计': 'design',
        '咨询': 'consulting',
        '培训': 'training',
        '教育': 'education',
        '卫生': 'health',
        '医疗': 'medical',
        '社会': 'social',
        '公共': 'public',
        '文化': 'culture',
        '体育': 'sports',
        '娱乐': 'entertainment',
        '旅游': 'tourism',
        '交通': 'transportation',
        '运输': 'transport',
        '仓储': 'storage',
        '物流': 'logistics',
        '邮政': 'postal',
        '通信': 'communication',
        '信息': 'information',
        '软件': 'software',
        '网络': 'network',
        '金融': 'finance',
        '保险': 'insurance',
        '银行': 'banking',
        '证券': 'securities',
        '房地产': 'real estate',
        '租赁': 'leasing',
        '商务': 'business',
        '住宿': 'accommodation',
        '餐饮': 'catering',
        '环境': 'environment',
        '环保': 'environmental protection',
        '能源': 'energy',
        '电力': 'electricity',
        '热力': 'heat',
        '燃气': 'gas',
        '水': 'water',
        '供应': 'supply',
        '建筑': 'construction',
        '工程': 'engineering',
        '施工': 'construction',
        '装饰': 'decoration',
        '设备': 'equipment',
        '机械': 'machinery',
        '工具': 'tools',
        '仪器': 'instruments',
        '材料': 'materials',
        '原料': 'raw materials',
        '产品': 'products',
        '商品': 'goods',
        '物品': 'items',
        '用品': 'supplies',
        '制品': 'products',
        '成品': 'finished products',
        '半成品': 'semi-finished products',
        '零件': 'parts',
        '部件': 'components',
        '配件': 'accessories',
        '工业': 'industry',
        '产业': 'industry',
        '行业': 'industry',
        '企业': 'enterprise',
        '公司': 'company',
        '厂': 'factory',
        '场': 'field',
        '站': 'station',
        '中心': 'center',
        '基地': 'base',
        '园区': 'park',
        '系统': 'system',
        '平台': 'platform',
        '网点': 'network',
        '机构': 'institution',
        '组织': 'organization',
        '协会': 'association',
        '学会': 'society',
        '联合会': 'federation',
        '委员会': 'committee',
        '办公室': 'office',
        '部门': 'department',
        '民政部门': 'civil affairs departments',
        '登记': 'registration',
        '宗教团体': 'religious organizations',
        '政府': 'government',
        '宗教事务部门': 'religious affairs departments',
        '宗教活动场所': 'religious activity venues',
        '选举': 'election',
        '产生': 'produce',
        '社区性组织': 'community organizations',
        '该组织': 'the organization',
        '本地区': 'local area',
        '一般性': 'general',
        '调解': 'mediation',
        '治安': 'public security',
        '优抚': 'preferential treatment',
        '计划生育': 'family planning',
        '城市': 'urban',
        '镇': 'town',
        '居民': 'residents',
        '群众性': 'mass',
        '自治组织': 'autonomous organizations',
        '管理活动': 'management activities',
        '农村': 'rural',
        '村民': 'villagers',
        '联合国': 'United Nations',
        '国际组织': 'international organizations',
        '驻我国': 'in China',
        '境内': 'within the territory',
        '机构': 'institutions',
        '其他活动': 'other activities',
    }
    
    text_str = str(text)
    for cn, en in sorted(descriptions.items(), key=lambda x: len(x[0]), reverse=True):
        if cn in text_str:
            text_str = text_str.replace(cn, en)
    
    # 清理多余空格
    text_str = re.sub(r'\s+', ' ', text_str)
    text_str = text_str.strip()
    
    return text_str

if __name__ == "__main__":
    complete_translation()