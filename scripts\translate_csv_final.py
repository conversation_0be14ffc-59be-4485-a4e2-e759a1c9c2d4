#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import re

def translate_text(text):
    """
    翻译中文文本到英文 - 更完善版本
    """
    if pd.isna(text) or text == '':
        return text
    
    # 完整的翻译字典
    translations = {
        # 基础术语
        '分类级别': 'Classification Level',
        '分类代码': 'Classification Code', 
        '分类名称': 'Classification Name',
        '上级代码': 'Parent Code',
        '核心关键词': 'Core Keywords',
        '说明': 'Description',
        
        # 分类级别
        '门类': 'Sector',
        '大类': 'Major Category',
        '中类': 'Medium Category', 
        '小类': 'Minor Category',
        
        # 连接词和介词
        '指对': 'Refers to',
        '指以': 'Refers to',
        '指用于': 'Refers to',
        '指在': 'Refers to',
        '指为': 'Refers to',
        '指从事': 'Refers to engaging in',
        '指应用': 'Refers to applying',
        '指通过': 'Refers to through',
        '指利用': 'Refers to using',
        '指主要': 'Refers to mainly',
        '指将': 'Refers to converting',
        '指把': 'Refers to converting',
        '指各种': 'Refers to various',
        '各种': 'various',
        '以及': 'and',
        '包括': 'including',
        '等': 'etc.',
        '等活动': 'and other activities',
        '等生产活动': 'and other production activities',
        '的生产活动': 'production activities',
        '的活动': 'activities',
        '的种植': 'cultivation',
        '的开采': 'mining',
        '的加工': 'processing',
        '的制造': 'manufacturing',
        '的采集': 'collection',
        '的饲养': 'raising',
        '的养殖': 'breeding',
        '的捕捞': 'fishing',
        '的采运': 'harvesting and transportation',
        '不包括': 'does not include',
        '不含': 'does not include',
        '还包括': 'also includes',
        '也包括': 'also includes',
        '以及其他': 'and other',
        '其他': 'other',
        '未列明': 'not listed elsewhere',
        '未列明的': 'not listed elsewhere',
        '其他未列明': 'other not listed elsewhere',
        '一般': 'general',
        '主要': 'main',
        '专门': 'specialized',
        '天然': 'natural',
        '人工': 'artificial',
        '野生': 'wild',
        '本门类包括': 'This sector includes',
        '本类包括': 'This category includes',
        '为了': 'in order to',
        '而从事': 'engaged in',
        '获得': 'obtain',
        '进行': 'conduct',
        '采取': 'take',
        '提供': 'provide',
        '经过': 'through',
        '通过': 'through',
        '利用': 'using',
        '使用': 'using',
        '应用': 'applying',
        '采用': 'adopting',
        '配备': 'equipped with',
        '配以': 'supplemented with',
        '辅以': 'assisted by',
        '辅料': 'auxiliary materials',
        '原料': 'raw materials',
        '材料': 'materials',
        '成型': 'molding',
        '油炸': 'frying',
        '烤制': 'baking',
        '而成': 'made from',
        '制成': 'made into',
        '加工制成': 'processed into',
        '经加工': 'after processing',
        '经': 'after',
        '后': 'after',
        '时': 'when',
        '中': 'in',
        '上': 'on',
        '下': 'under',
        '内': 'inside',
        '外': 'outside',
        '间': 'between',
        '与': 'and',
        '及': 'and',
        '或': 'or',
        '但': 'but',
        '除': 'except',
        '由': 'by',
        '从': 'from',
        '至': 'to',
        '到': 'to',
        '向': 'to',
        '对': 'to',
        '为': 'for',
        '用': 'use',
        '作': 'as',
        '成': 'into',
        '于': 'at',
        '在': 'in',
        '将': 'will',
        '把': 'to',
        '使': 'make',
        '让': 'let',
        
        # 数量词
        '一': 'one',
        '二': 'two',
        '三': 'three',
        '四': 'four',
        '五': 'five',
        '六': 'six',
        '七': 'seven',
        '八': 'eight',
        '九': 'nine',
        '十': 'ten',
        '百': 'hundred',
        '千': 'thousand',
        '万': 'ten thousand',
        '切': 'all',
        '一切': 'all',
        '全部': 'all',
        '整个': 'entire',
        '所有': 'all',
        '各个': 'each',
        '每个': 'each',
        '单个': 'single',
        '多个': 'multiple',
        '若干': 'several',
        '许多': 'many',
        '大量': 'large amount',
        '少量': 'small amount',
        '批量': 'batch',
        '大批': 'large batch',
        '小批': 'small batch',
        '个': '',
        '种': 'types of',
        '类': 'categories',
        '项': 'items',
        '批': 'batches',
        '次': 'times',
        '年': 'years',
        '月': 'months',
        '日': 'days',
        
        # 农业相关
        '农、林、牧、渔业': 'Agriculture, Forestry, Animal Husbandry, and Fishery',
        '农业': 'agriculture',
        '林业': 'forestry', 
        '畜牧业': 'animal husbandry',
        '渔业': 'fishery',
        '种植业': 'planting industry',
        '养殖业': 'breeding industry',
        '农业生产': 'agricultural production',
        '农作物': 'crops',
        '作物生产': 'crop production',
        '农产品': 'agricultural products',
        '谷物': 'grain',
        '稻谷': 'rice',
        '小麦': 'wheat',
        '玉米': 'corn',
        '大豆': 'soybean',
        '豆类': 'legumes',
        '油料': 'oil crops',
        '薯类': 'tubers',
        '棉花': 'cotton',
        '麻类': 'hemp',
        '糖料': 'sugar crops',
        '烟草': 'tobacco',
        '蔬菜': 'vegetables',
        '食用菌': 'edible fungi',
        '花卉': 'flowers',
        '园艺': 'horticulture',
        '园艺作物': 'horticultural crops',
        '水果': 'fruits',
        '仁果类': 'pome fruits',
        '核果类': 'stone fruits',
        '葡萄': 'grapes',
        '柑橘类': 'citrus',
        '香蕉': 'bananas',
        '亚热带': 'subtropical',
        '亚热带水果': 'subtropical fruits',
        '坚果': 'nuts',
        '含油果': 'oil-bearing fruits',
        '香料': 'spices',
        '饮料': 'beverages',
        '饮料作物': 'beverage crops',
        '茶叶': 'tea',
        '中药材': 'traditional Chinese medicine materials',
        '中草药': 'traditional Chinese herbal medicine',
        '草': 'grass',
        '牧草': 'pasture grass',
        '草原': 'grassland',
        '刈割': 'cutting',
        '收获': 'harvest',
        '籽实': 'seeds',
        '饲料': 'feed',
        '工业': 'industry',
        '种植': 'cultivation',
        '栽培': 'cultivation',
        '培育': 'cultivation',
        '培养': 'cultivation',
        '育种': 'breeding',
        '育苗': 'seedling raising',
        '繁殖': 'reproduction',
        '繁育': 'breeding',
        '苗木': 'seedlings',
        '种子': 'seeds',
        '种苗': 'seedlings',
        '良种': 'improved varieties',
        '品种': 'varieties',
        '新品种': 'new varieties',
        '遗传': 'genetic',
        '遗传学': 'genetics',
        '原理': 'principles',
        '选育': 'breeding',
        '栽植': 'planting',
        '改良': 'improvement',
        '穗条': 'shoots',
        '植物': 'plants',
        '组织': 'tissues',
        '造林': 'afforestation',
        '更新': 'renewal',
        '宜林': 'suitable for forestry',
        '荒山': 'barren mountains',
        '荒地': 'wasteland',
        '荒沙': 'sandy wasteland',
        '采伐': 'logging',
        '迹地': 'cut-over land',
        '火烧': 'burned',
        '疏林': 'sparse forest',
        '灌木': 'shrubs',
        '林地': 'forest land',
        '土地': 'land',
        '封山': 'mountain closure',
        '育林': 'forest cultivation',
        '飞播': 'aerial seeding',
        '方式': 'methods',
        '恢复': 'restoration',
        '森林': 'forests',
        '林木': 'forest trees',
        '生长': 'growth',
        '发育': 'development',
        '时期': 'periods',
        '不同': 'different',
        '促进': 'promote',
        '调整': 'adjust',
        '林分': 'forest stand',
        '结构': 'structure',
        '树种': 'tree species',
        '组成': 'composition',
        '形成': 'form',
        '密度': 'density',
        '合理': 'reasonable',
        '物种': 'species',
        '丰富': 'rich',
        '功能': 'function',
        '完备': 'complete',
        '优质': 'high quality',
        '高产': 'high yield',
        '高效': 'high efficiency',
        '抚育': 'tending',
        '补植': 'replanting',
        '补播': 'resowing',
        '措施': 'measures',
        '木材': 'wood',
        '竹材': 'bamboo',
        '竹木': 'bamboo and wood',
        '运出': 'transport out',
        '山场': 'mountain sites',
        '贮木场': 'storage yards',
        '林产品': 'forest products',
        '采集': 'collection',
        '野生': 'wild',
        '木竹材': 'wood and bamboo',
        '非木竹材': 'non-wood and non-bamboo',
        
        # 畜牧业相关
        '牲畜': 'livestock',
        '家禽': 'poultry',
        '动物': 'animals',
        '畜禽': 'livestock and poultry',
        '牛': 'cattle',
        '马': 'horses',
        '猪': 'pigs',
        '羊': 'sheep',
        '骆驼': 'camels',
        '鸡': 'chickens',
        '鸭': 'ducks',
        '鹅': 'geese',
        '兔': 'rabbits',
        '蜜蜂': 'bees',
        '饲养': 'raising',
        '养殖': 'breeding',
        '狩猎': 'hunting',
        '捕捉': 'capturing',
        '相关': 'related',
        '圈舍': 'pens',
        '清理': 'cleaning',
        '产品': 'products',
        '畜产品': 'livestock products',
        '免疫': 'immunization',
        '接种': 'vaccination',
        '标识': 'identification',
        '佩戴': 'wearing',
        '诊疗': 'diagnosis and treatment',
        '粪污': 'manure',
        
        # 渔业相关
        '水产': 'aquatic products',
        '水产品': 'aquatic products',
        '海水': 'seawater',
        '内陆': 'inland',
        '淡水': 'freshwater',
        '海洋': 'marine',
        '水域': 'waters',
        '水生': 'aquatic',
        '动植物': 'animals and plants',
        '鱼类': 'fish',
        '虾类': 'shrimp',
        '贝类': 'shellfish',
        '藻类': 'algae',
        '捕捞': 'fishing',
        '鱼苗': 'fish fry',
        '鱼种': 'fish seed',
        '鱼种场': 'fish seed farms',
        '增殖': 'enhancement',
        '增殖场': 'enhancement farms',
        
        # 专业和辅助性活动
        '专业': 'professional',
        '辅助性': 'auxiliary',
        '辅助性活动': 'auxiliary activities',
        '专业及辅助性活动': 'professional and auxiliary activities',
        '机械': 'machinery',
        '机械活动': 'machinery activities',
        '操作': 'operation',
        '人员': 'personnel',
        '操作人员': 'operators',
        '灌溉': 'irrigation',
        '排水': 'drainage',
        '系统': 'system',
        '经营': 'operation',
        '管理': 'management',
        '初加工': 'primary processing',
        '脱水': 'dehydration',
        '凝固': 'solidification',
        '打蜡': 'waxing',
        '去籽': 'deseeding',
        '净化': 'purification',
        '分类': 'classification',
        '晒干': 'sun drying',
        '剥皮': 'peeling',
        '初烤': 'primary baking',
        '沤软': 'retting',
        '包装': 'packaging',
        '初级': 'primary',
        '市场': 'market',
        '服务': 'service',
        '天然': 'natural',
        '橡胶': 'rubber',
        '纺织': 'textile',
        '纤维': 'fiber',
        '棉纤维': 'cotton fiber',
        '短绒': 'short lint',
        '剥离': 'stripping',
        '棉籽': 'cottonseed',
        '秸秆': 'straw',
        '铃壳': 'boll shells',
        '副产品': 'by-products',
        '综合': 'comprehensive',
        '病虫害': 'pests and diseases',
        '防治': 'prevention and control',
        '重大': 'major',
        '代耕': 'contract farming',
        '代种': 'contract planting',
        '代收': 'contract harvesting',
        '大田': 'field',
        '托管': 'trusteeship',
        '有害': 'harmful',
        '生物': 'organisms',
        '防火': 'fire prevention',
        '去皮': 'bark removal',
        '打枝': 'pruning',
        '去料': 'debris removal',
        '初包装': 'primary packaging',
        
        # 采矿业相关
        '采矿业': 'mining industry',
        '矿业': 'mining',
        '资源': 'resources',
        '开采': 'mining',
        '矿物': 'minerals',
        '煤炭': 'coal',
        '石油': 'oil',
        '天然气': 'natural gas',
        '金属': 'metals',
        '非金属': 'non-metals',
        '固体': 'solid',
        '液体': 'liquid',
        '气体': 'gas',
        '自然': 'natural',
        '产生': 'produced',
        '采掘': 'extraction',
        '地下': 'underground',
        '地上': 'surface',
        '矿井': 'mines',
        '运行': 'operation',
        '矿址': 'mine sites',
        '附近': 'nearby',
        '旨在': 'aimed at',
        '原材料': 'raw materials',
        '辅助性': 'auxiliary',
        '工作': 'work',
        '碾磨': 'grinding',
        '选矿': 'beneficiation',
        '销售': 'sales',
        '准备': 'preparation',
        '工作': 'work',
        '蓄集': 'collection',
        '净化': 'purification',
        '分配': 'distribution',
        '地质': 'geological',
        '勘查': 'exploration',
        '建筑': 'construction',
        '工程': 'engineering',
        '活动': 'activities',
        '再制造': 'remanufacturing',
        '废旧': 'waste',
        '汽车': 'automotive',
        '零部件': 'parts',
        '机械': 'machinery',
        '机床': 'machine tools',
        '专业化': 'specialized',
        '修复': 'restoration',
        '批量化': 'batch',
        '过程': 'process',
        '质量': 'quality',
        '性能': 'performance',
        '煤炭': 'coal',
        '洗选': 'washing and selection',
        '分级': 'grading',
        '煤制品': 'coal products',
        '勘探': 'exploration',
        '烟煤': 'bituminous coal',
        '无烟煤': 'anthracite',
        '露天': 'open-pit',
        '采出': 'mined',
        '硬煤': 'hard coal',
        '褐煤': 'lignite',
        '煤化': 'coalification',
        '程度': 'degree',
        '燃料': 'fuel',
        '石煤': 'stone coal',
        '泥炭': 'peat',
        '古生代': 'Paleozoic',
        '地层': 'strata',
        '含碳量': 'carbon content',
        '灰分': 'ash content',
        '陆地': 'land',
        '海洋': 'ocean',
        '原油': 'crude oil',
        '瓦斯': 'gas',
        '煤矿': 'coal mines',
        '煤层气': 'coalbed methane',
        '运输': 'transportation',
        '目的': 'purpose',
        '液化': 'liquefaction',
        '气体': 'gas',
        '液化烃': 'liquefied hydrocarbons',
        '沥青': 'asphalt',
        '页岩': 'shale',
        '油母': 'oil shale',
        '焦油': 'tar',
        '沙矿': 'sand ore',
        '同类': 'similar',
        '作业': 'operations',
        '黑色': 'ferrous',
        '有色': 'non-ferrous',
        '铁矿': 'iron ore',
        '锰矿': 'manganese ore',
        '铬矿': 'chromium ore',
        '钒矿': 'vanadium ore',
        '钢铁': 'steel',
        '辅助': 'auxiliary',
        '常用': 'common',
        '贵金属': 'precious metals',
        '稀有': 'rare',
        '稀土': 'rare earth',
        '深海': 'deep sea',
        '铜': 'copper',
        '铅锌': 'lead-zinc',
        '镍钴': 'nickel-cobalt',
        '锡': 'tin',
        '锑': 'antimony',
        '铝': 'aluminum',
        '镁': 'magnesium',
        '汞': 'mercury',
        '镉': 'cadmium',
        '铋': 'bismuth',
        '金': 'gold',
        '银': 'silver',
        '铂族': 'platinum group',
        '元素': 'elements',
        '铂': 'platinum',
        '铱': 'iridium',
        '锇': 'osmium',
        '钌': 'ruthenium',
        '钯': 'palladium',
        '铑': 'rhodium',
        '地壳': 'earth\'s crust',
        '含量': 'content',
        '极少': 'extremely small',
        '自然界': 'nature',
        '分布': 'distribution',
        '稀散': 'scattered',
        '难以': 'difficult',
        '提取': 'extraction',
        '研究': 'research',
        '钨钼': 'tungsten-molybdenum',
        '镧系': 'lanthanide',
        '性质': 'properties',
        '相近': 'similar',
        '钍': 'thorium',
        '铀': 'uranium',
        '矿石': 'ore',
        '精选': 'beneficiation',
        '轻金属': 'light metals',
        '高熔点': 'high melting point',
        '稀散': 'scattered',
        '土砂石': 'earth and stone',
        '石灰石': 'limestone',
        '石膏': 'gypsum',
        '石灰': 'lime',
        '助熔剂': 'flux',
        '装饰': 'decoration',
        '装饰用': 'decorative',
        '采石场': 'quarry',
        '切制': 'cutting',
        '纪念碑': 'monuments',
        '石料': 'stone',
        '耐火': 'refractory',
        '土石': 'earth and stone',
        '陶瓷': 'ceramics',
        '粘土': 'clay',
        '铺路': 'paving',
        '建筑': 'construction',
        '建筑材料': 'building materials',
        '石渣': 'stone chips',
        '砂': 'sand',
        '化学矿': 'chemical minerals',
        '肥料': 'fertilizer',
        '海底': 'submarine',
        '盐': 'salt',
        '海水': 'seawater',
        '沿海': 'coastal',
        '浅层': 'shallow',
        '卤水': 'brine',
        '晒制': 'sun drying',
        '钻井': 'drilling',
        '汲取': 'extraction',
        '注水': 'water injection',
        '溶解': 'dissolution',
        '岩盐': 'rock salt',
        '真空': 'vacuum',
        '蒸发': 'evaporation',
        '干燥': 'drying',
        '盐湖': 'salt lakes',
        '采掘': 'mining',
        '氯化钠': 'sodium chloride',
        '成分': 'components',
        '粉碎': 'crushing',
        '筛选': 'screening',
        '石棉': 'asbestos',
        '云母': 'mica',
        '石墨': 'graphite',
        '滑石': 'talc',
        '磨料': 'abrasives',
        '宝石': 'gemstones',
        '玉石': 'jade',
        '彩石': 'colored stones',
        
        # 制造业相关
        '制造业': 'manufacturing',
        '制造': 'manufacturing',
        '物理': 'physical',
        '化学': 'chemical',
        '变化': 'change',
        '新产品': 'new products',
        '动力': 'power',
        '手工': 'manual',
        '制作': 'making',
        '批发': 'wholesale',
        '零售': 'retail',
        '视为': 'considered as',
        '建筑物': 'buildings',
        '制成品': 'finished products',
        '零部件': 'parts',
        '预制品': 'prefabricated',
        '工地': 'construction site',
        '主要': 'main',
        '部件': 'components',
        '组装': 'assembly',
        '桥梁': 'bridges',
        '仓库': 'warehouse',
        '设备': 'equipment',
        '铁路': 'railways',
        '高架': 'elevated',
        '公路': 'highways',
        '升降机': 'elevators',
        '电梯': 'lifts',
        '管道': 'pipelines',
        '喷水': 'sprinkler',
        '暖气': 'heating',
        '通风': 'ventilation',
        '空调': 'air conditioning',
        '照明': 'lighting',
        '安装': 'installation',
        '电线': 'wires',
        '装置': 'installations',
        '列为': 'classified as',
        '机电': 'electromechanical',
        
        # 农副食品加工业
        '农副食品': 'agricultural and food products',
        '农副食品加工业': 'agricultural and food processing industry',
        '直接': 'directly',
        '磨制': 'milling',
        '屠宰': 'slaughter',
        '肉类': 'meat',
        '蔬菜': 'vegetables',
        '水果': 'fruits',
        '谷物磨制': 'grain milling',
        '粮食': 'grain',
        '稻谷': 'rice',
        '谷子': 'millet',
        '高粱': 'sorghum',
        '去壳': 'hulling',
        '碾磨': 'milling',
        '成品粮': 'finished grain',
        '稻谷加工': 'rice processing',
        '大米': 'rice',
        '小麦加工': 'wheat processing',
        '小麦粉': 'wheat flour',
        '玉米加工': 'corn processing',
        '碾碎': 'crushing',
        '玉米碴': 'corn grits',
        '玉米粉': 'corn flour',
        '杂粮': 'miscellaneous grains',
        '绿豆': 'mung beans',
        '红小豆': 'red beans',
        '小宗': 'minor',
        '清理': 'cleaning',
        '饲料加工': 'feed processing',
        '宠物': 'pet',
        '合法': 'legal',
        '猫': 'cats',
        '狗': 'dogs',
        '鱼': 'fish',
        '鸟': 'birds',
        '小动物': 'small animals',
        '食物': 'food',
        '适用': 'suitable',
        '农场': 'farms',
        '农户': 'farmers',
        '水产品': 'aquatic products',
        '低值': 'low-value',
        '废弃物': 'waste',
        '鱼骨': 'fish bones',
        '内脏': 'internal organs',
        '虾壳': 'shrimp shells',
        '植物油': 'vegetable oil',
        '油料': 'oil crops',
        '油脂': 'oils and fats',
        '精制': 'refined',
        '食用': 'edible',
        '非食用': 'non-edible',
        '制糖': 'sugar production',
        '甘蔗': 'sugarcane',
        '甜菜': 'sugar beet',
        '砂糖': 'granulated sugar',
        '精炼': 'refining',
        '精制糖': 'refined sugar',
        '屠宰': 'slaughter',
        '宰杀': 'slaughter',
        '鲜肉': 'fresh meat',
        '冷冻': 'freezing',
        '保鲜': 'preservation',
        '商业': 'commercial',
        '冷藏': 'refrigeration',
        '肉制品': 'meat products',
        '熟肉': 'cooked meat',
        '冷冻加工': 'freezing processing',
        '甲壳类': 'crustaceans',
        '贝类': 'shellfish',
        '水生': 'aquatic',
        '鱼糜': 'fish paste',
        '干制': 'drying',
        '腌制': 'curing',
        '鱼油': 'fish oil',
        '鱼肝': 'fish liver',
        '脱水': 'dehydration',
        '干燥': 'drying',
        '菌类': 'fungi',
        '淀粉': 'starch',
        '薯类': 'tubers',
        '豆类': 'legumes',
        '酶法': 'enzymatic',
        '酸法': 'acid',
        '转换': 'conversion',
        '糖品': 'sugar products',
        '豆制品': 'soy products',
        '小豆': 'small beans',
        '豌豆': 'peas',
        '蚕豆': 'broad beans',
        '蛋品': 'egg products',
        
        # 食品制造业
        '食品制造业': 'food manufacturing',
        '焙烤': 'baking',
        '焙烤食品': 'baked foods',
        '糕点': 'cakes',
        '面包': 'bread',
        '米粉': 'rice flour',
        '豆粉': 'bean flour',
        '辅料': 'auxiliary materials',
        '成型': 'molding',
        '油炸': 'frying',
        '烤制': 'baking',
        '饼干': 'biscuits',
        '糯米粉': 'glutinous rice flour',
        '糖': 'sugar',
        '油脂': 'fats',
        '奶制品': 'dairy products',
        '蛋制品': 'egg products',
        '焙烤': 'baking',
        '易于': 'easy to',
        '保存': 'preserve',
        '食用': 'eat',
        '方便': 'convenient',
        '糖果': 'candy',
        '巧克力': 'chocolate',
        '蜜饯': 'preserved fruits',
        '砂糖': 'granulated sugar',
        '葡萄糖': 'glucose',
        '糖浆': 'syrup',
        '饴糖': 'malt sugar',
        '乳品': 'dairy products',
        '胶体': 'colloid',
        '果仁': 'nuts',
        '香料': 'spices',
        '色素': 'pigments',
        '甜味': 'sweet',
        '块状': 'block',
        '浆状': 'paste',
        '粉状': 'powder',
        '可可': 'cocoa',
        '可可脂': 'cocoa butter',
        '可可酱': 'cocoa paste',
        '果皮': 'fruit peels',
        '部分': 'parts',
        '方便食品': 'convenience foods',
        '杂粮': 'miscellaneous grains',
        '简单': 'simple',
        '烹制': 'cooking',
        '主食': 'staple food',
        '携带': 'carrying',
        '储藏': 'storage',
        '特点': 'characteristics',
        '米': 'rice',
        '面制品': 'flour products',
        '未经': 'not',
        '蒸煮': 'steaming',
        '速冻': 'quick-frozen',
        '肉类': 'meat',
        '烹制': 'cooked',
        '未烹制': 'uncooked',
        '立即': 'immediately',
        '工艺': 'technology',
        '冻结': 'frozen',
        '条件': 'conditions',
        '储存': 'storage',
        '方便面': 'instant noodles',
        '蒸煮': 'steaming',
        '乳制品': 'dairy products',
        '生鲜': 'fresh',
        '牛': 'cow',
        '羊': 'sheep',
        '乳': 'milk',
        '液体': 'liquid',
        '固体': 'solid',
        '乳粉': 'milk powder',
        '炼乳': 'condensed milk',
        '乳脂肪': 'milk fat',
        '干酪': 'cheese',
        '含乳': 'milk-containing',
        '植物蛋白': 'plant protein',
        '饮料': 'beverages',
        
        # 通用术语
        '活动': 'activities',
        '生产': 'production',
        '生产活动': 'production activities',
        '经营': 'operation',
        '管理': 'management',
        '服务': 'service',
        '技术': 'technology',
        '科学': 'science',
        '研究': 'research',
        '开发': 'development',
        '设计': 'design',
        '咨询': 'consulting',
        '培训': 'training',
        '教育': 'education',
        '卫生': 'health',
        '医疗': 'medical',
        '社会': 'social',
        '公共': 'public',
        '文化': 'culture',
        '体育': 'sports',
        '娱乐': 'entertainment',
        '旅游': 'tourism',
        '交通': 'transportation',
        '运输': 'transport',
        '仓储': 'storage',
        '物流': 'logistics',
        '邮政': 'postal',
        '通信': 'communication',
        '信息': 'information',
        '软件': 'software',
        '网络': 'network',
        '金融': 'finance',
        '保险': 'insurance',
        '银行': 'banking',
        '证券': 'securities',
        '房地产': 'real estate',
        '租赁': 'leasing',
        '商务': 'business',
        '住宿': 'accommodation',
        '餐饮': 'catering',
        '环境': 'environment',
        '环保': 'environmental protection',
        '能源': 'energy',
        '电力': 'electricity',
        '热力': 'heat',
        '燃气': 'gas',
        '水': 'water',
        '供应': 'supply',
        '建筑': 'construction',
        '工程': 'engineering',
        '施工': 'construction',
        '装饰': 'decoration',
        '设备': 'equipment',
        '机械': 'machinery',
        '工具': 'tools',
        '仪器': 'instruments',
        '材料': 'materials',
        '原料': 'raw materials',
        '产品': 'products',
        '商品': 'goods',
        '物品': 'items',
        '用品': 'supplies',
        '制品': 'products',
        '成品': 'finished products',
        '半成品': 'semi-finished products',
        '零件': 'parts',
        '部件': 'components',
        '配件': 'accessories',
        '工业': 'industry',
        '产业': 'industry',
        '行业': 'industry',
        '企业': 'enterprise',
        '公司': 'company',
        '厂': 'factory',
        '场': 'field',
        '站': 'station',
        '中心': 'center',
        '基地': 'base',
        '园区': 'park',
        '系统': 'system',
        '平台': 'platform',
        '网点': 'network',
        '机构': 'institution',
        '组织': 'organization',
        '协会': 'association',
        '学会': 'society',
        '联合会': 'federation',
        '委员会': 'committee',
        '办公室': 'office',
        '部门': 'department',
        
        # 特殊符号和标点
        '～': '-',
        '、': ', ',
        '。': '.',
        '，': ', ',
        '；': '; ',
        '：': ': ',
        '？': '?',
        '！': '!',
        '（': '(',
        '）': ')',
        '【': '[',
        '】': ']',
        '《': '<',
        '》': '>',
        '"': '"',
        '"': '"',
        ''': "'",
        ''': "'",
        '—': '-',
        '–': '-',
        '…': '...',
        '·': '·',
        '％': '%',
        '‰': '‰',
        '§': '§',
        '©': '©',
        '®': '®',
        '™': '™',
        
        # 数字和符号
        '０': '0',
        '１': '1',
        '２': '2',
        '３': '3',
        '４': '4',
        '５': '5',
        '６': '6',
        '７': '7',
        '８': '8',
        '９': '9',
        '〇': '0',
        '○': '0',
        '●': '●',
        '◎': '◎',
        '△': '△',
        '▲': '▲',
        '□': '□',
        '■': '■',
        '◇': '◇',
        '◆': '◆',
        '☆': '☆',
        '★': '★',
        '※': '※',
        '＊': '*',
        '＋': '+',
        '－': '-',
        '×': '×',
        '÷': '÷',
        '＝': '=',
        '≠': '≠',
        '≤': '≤',
        '≥': '≥',
        '＜': '<',
        '＞': '>',
        '∞': '∞',
        '∑': '∑',
        '∏': '∏',
        '∫': '∫',
        '∮': '∮',
        '∝': '∝',
        '∴': '∴',
        '∵': '∵',
        '∶': ':',
        '∷': '::',
        '∠': '∠',
        '⊥': '⊥',
        '∥': '∥',
        '∦': '∦',
        '∈': '∈',
        '∉': '∉',
        '∋': '∋',
        '∌': '∌',
        '⊂': '⊂',
        '⊃': '⊃',
        '⊆': '⊆',
        '⊇': '⊇',
        '∪': '∪',
        '∩': '∩',
        '∅': '∅',
        '∃': '∃',
        '∀': '∀',
        '∧': '∧',
        '∨': '∨',
        '¬': '¬',
        '→': '→',
        '←': '←',
        '↑': '↑',
        '↓': '↓',
        '↔': '↔',
        '⇒': '⇒',
        '⇐': '⇐',
        '⇑': '⇑',
        '⇓': '⇓',
        '⇔': '⇔',
        '√': '√',
        '∛': '∛',
        '∜': '∜',
        '℃': '℃',
        '℉': '℉',
        '°': '°',
        '′': "'",
        '″': '"',
        '‴': '‴',
        'Å': 'Å',
        'α': 'α',
        'β': 'β',
        'γ': 'γ',
        'δ': 'δ',
        'ε': 'ε',
        'ζ': 'ζ',
        'η': 'η',
        'θ': 'θ',
        'ι': 'ι',
        'κ': 'κ',
        'λ': 'λ',
        'μ': 'μ',
        'ν': 'ν',
        'ξ': 'ξ',
        'ο': 'ο',
        'π': 'π',
        'ρ': 'ρ',
        'σ': 'σ',
        'τ': 'τ',
        'υ': 'υ',
        'φ': 'φ',
        'χ': 'χ',
        'ψ': 'ψ',
        'ω': 'ω',
        'Α': 'Α',
        'Β': 'Β',
        'Γ': 'Γ',
        'Δ': 'Δ',
        'Ε': 'Ε',
        'Ζ': 'Ζ',
        'Η': 'Η',
        'Θ': 'Θ',
        'Ι': 'Ι',
        'Κ': 'Κ',
        'Λ': 'Λ',
        'Μ': 'Μ',
        'Ν': 'Ν',
        'Ξ': 'Ξ',
        'Ο': 'Ο',
        'Π': 'Π',
        'Ρ': 'Ρ',
        'Σ': 'Σ',
        'Τ': 'Τ',
        'Υ': 'Υ',
        'Φ': 'Φ',
        'Χ': 'Χ',
        'Ψ': 'Ψ',
        'Ω': 'Ω',
    }
    
    # 处理文本
    result = str(text).strip()
    
    # 如果是分号分隔的关键词，分别处理
    if ';' in result:
        keywords = result.split(';')
        translated_keywords = []
        for keyword in keywords:
            keyword = keyword.strip()
            # 对每个关键词进行翻译
            translated_keyword = translate_single_text(keyword, translations)
            translated_keywords.append(translated_keyword)
        return ';'.join(translated_keywords)
    
    # 处理普通文本
    return translate_single_text(result, translations)

def translate_single_text(text, translations):
    """
    翻译单个文本片段
    """
    result = text
    
    # 按长度排序，先替换长的词汇，避免短词汇误替换
    sorted_translations = sorted(translations.items(), key=lambda x: len(x[0]), reverse=True)
    
    for chinese, english in sorted_translations:
        if chinese in result:
            result = result.replace(chinese, english)
    
    return result

def translate_csv_file(input_file, output_file):
    """
    翻译CSV文件
    """
    print(f"正在读取文件: {input_file}")
    
    # 读取CSV文件
    try:
        df = pd.read_csv(input_file, encoding='utf-8')
    except UnicodeDecodeError:
        try:
            df = pd.read_csv(input_file, encoding='gbk')
        except UnicodeDecodeError:
            df = pd.read_csv(input_file, encoding='gb2312')
    
    print(f"文件包含 {len(df)} 行数据")
    
    # 翻译每一列
    for column in df.columns:
        print(f"正在翻译列: {column}")
        df[column] = df[column].apply(translate_text)
    
    # 保存翻译后的文件
    print(f"正在保存翻译后的文件: {output_file}")
    df.to_csv(output_file, index=False, encoding='utf-8')
    
    print(f"翻译完成！共处理了 {len(df)} 行数据")

if __name__ == "__main__":
    input_file = "GB_T_4754_2017_converted.csv"
    output_file = "GB_T_4754_2017_converted_english_final.csv"
    
    translate_csv_file(input_file, output_file)