#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import re

def fix_translation(input_file, output_file):
    """
    修复翻译文件
    """
    print(f"正在读取文件: {input_file}")
    
    # 读取CSV文件
    try:
        df = pd.read_csv(input_file, encoding='utf-8')
    except UnicodeDecodeError:
        try:
            df = pd.read_csv(input_file, encoding='gbk')
        except UnicodeDecodeError:
            df = pd.read_csv(input_file, encoding='gb2312')
    
    # 手动修复表头
    df.columns = ['Classification Level', 'Classification Code', 'Classification Name', 'Parent Code', 'Core Keywords', 'Description']
    
    # 修复词典
    fix_dict = {
        # 表头修复
        '分类级别': 'Classification Level',
        '分类代码': 'Classification Code',
        '分类名称': 'Classification Name',
        '上级代码': 'Parent Code',
        '核心关键词': 'Core Keywords',
        '说明': 'Description',
        
        # 常见问题修复
        'This sector includes01-05Major Category': 'This sector includes major categories 01-05',
        'This category includes06-12Major Category': 'This category includes major categories 06-12',
        'This category includes13-43Major Category': 'This category includes major categories 13-43',
        'Refers tovarious': 'Refers to various',
        'Refers toharvestseeds': 'Refers to harvesting seeds',
        'for主的': 'as main',
        'cornetc.': 'corn etc.',
        '和as': ' and as',
        'forfeed': 'for feed',
        '和industry': ' and industry',
        
        # 连在一起的词修复
        'graincultivation': 'grain cultivation',
        'ricecultivation': 'rice cultivation',
        'wheatcultivation': 'wheat cultivation',
        'corncultivation': 'corn cultivation',
        'legumescultivation': 'legume cultivation',
        'cottoncultivation': 'cotton cultivation',
        'hempcultivation': 'hemp cultivation',
        'tobaccocultivation': 'tobacco cultivation',
        'vegetablescultivation': 'vegetable cultivation',
        'flowercultivation': 'flower cultivation',
        'fruitcultivation': 'fruit cultivation',
        'nutcultivation': 'nut cultivation',
        'teacultivation': 'tea cultivation',
        
        # 中文残留修复
        '棉': 'cotton',
        '麻': 'hemp',
        '和': ' and ',
        '的': '',
        '主的': 'main',
        '等': 'etc.',
        '各种': 'various',
        '其他': 'other',
        '指对': 'refers to',
        '指以': 'refers to',
        '指用于': 'refers to',
        '指在': 'refers to',
        '指为': 'refers to',
        '包括': 'including',
        '以及': 'and',
        '不包括': 'does not include',
        '还包括': 'also includes',
        
        # 空格修复
        '  ': ' ',
        '   ': ' ',
        ' ,': ',',
        ' .': '.',
        ' ;': ';',
        ' :': ':',
    }
    
    # 应用修复
    print("正在应用修复...")
    for column in df.columns:
        print(f"修复列: {column}")
        df[column] = df[column].astype(str).apply(lambda x: fix_text(x, fix_dict))
    
    # 保存修复后的文件
    print(f"正在保存修复后的文件: {output_file}")
    df.to_csv(output_file, index=False, encoding='utf-8')
    
    print(f"修复完成！共处理了 {len(df)} 行数据")

def fix_text(text, fix_dict):
    """
    修复文本
    """
    if pd.isna(text) or text == 'nan':
        return ''
    
    result = str(text)
    
    # 应用修复字典
    for old, new in sorted(fix_dict.items(), key=lambda x: len(x[0]), reverse=True):
        if old in result:
            result = result.replace(old, new)
    
    # 清理多余空格
    result = re.sub(r'\s+', ' ', result)
    result = result.strip()
    
    return result

if __name__ == "__main__":
    input_file = "GB_T_4754_2017_converted_english_final.csv"
    output_file = "GB_T_4754_2017_converted_english_fixed.csv"
    
    fix_translation(input_file, output_file)