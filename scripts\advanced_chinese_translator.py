#!/usr/bin/env python3
"""
Advanced Chinese Text Translator with Deduplication and Checkpointing

This program translates Chinese text in CSV files to English with:
- Intelligent deduplication to avoid redundant translations
- Robust checkpoint/resume functionality for handling interruptions
- Concurrent API processing for optimal performance
- Real-time progress tracking with detailed statistics
- Multilingual Excel output with separate sheets

Author: Augment Agent
Date: 2025-07-01
"""

import asyncio
import json
import logging
import os
import re
import time
import pickle
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Set, Tuple, Optional, Any
from collections import defaultdict
import hashlib

import pandas as pd
import numpy as np
from openai import AsyncOpenAI
import aiofiles

# Configure logging
def setup_logging():
    """Setup comprehensive logging configuration."""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_filename = f"advanced_translation_{timestamp}.log"
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_filename, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    return log_filename

# Constants
CONFIG_FILE = "infini_config.json"
CHECKPOINT_INTERVAL = 100  # Save checkpoint every N translations
BATCH_SIZE = 10  # Number of concurrent translations
MAX_RETRIES = 3
PROGRESS_UPDATE_INTERVAL = 50  # Update progress every N translations

class TranslationCheckpoint:
    """Manages translation checkpoints for resume functionality."""
    
    def __init__(self, checkpoint_dir: str = "checkpoints"):
        self.checkpoint_dir = Path(checkpoint_dir)
        self.checkpoint_dir.mkdir(exist_ok=True)
        self.logger = logging.getLogger(__name__)
    
    def save_checkpoint(self, data: Dict[str, Any], checkpoint_name: str):
        """Save checkpoint data to file."""
        checkpoint_file = self.checkpoint_dir / f"{checkpoint_name}.pkl"
        try:
            with open(checkpoint_file, 'wb') as f:
                pickle.dump(data, f)
            self.logger.info(f"Checkpoint saved: {checkpoint_file}")
        except Exception as e:
            self.logger.error(f"Failed to save checkpoint: {e}")
    
    def load_checkpoint(self, checkpoint_name: str) -> Optional[Dict[str, Any]]:
        """Load checkpoint data from file."""
        checkpoint_file = self.checkpoint_dir / f"{checkpoint_name}.pkl"
        if not checkpoint_file.exists():
            return None
        
        try:
            with open(checkpoint_file, 'rb') as f:
                data = pickle.load(f)
            self.logger.info(f"Checkpoint loaded: {checkpoint_file}")
            return data
        except Exception as e:
            self.logger.error(f"Failed to load checkpoint: {e}")
            return None
    
    def list_checkpoints(self) -> List[str]:
        """List available checkpoint files."""
        return [f.stem for f in self.checkpoint_dir.glob("*.pkl")]

class ChineseTextAnalyzer:
    """Analyzes and categorizes text for translation optimization."""
    
    def __init__(self):
        self.chinese_pattern = re.compile(r'[\u4e00-\u9fff]+')
        self.numeric_pattern = re.compile(r'^[\d\s\.\,\-\+\(\)\/]+$')
        self.date_pattern = re.compile(r'^\d{4}[\/\-]\d{1,2}[\/\-]\d{1,2}$')
        self.english_pattern = re.compile(r'^[a-zA-Z\s\.\,\-\+\(\)\/]+$')
    
    def contains_chinese(self, text: str) -> bool:
        """Check if text contains Chinese characters."""
        if pd.isna(text) or not isinstance(text, str):
            return False
        return bool(self.chinese_pattern.search(text))
    
    def is_translatable(self, text: str) -> bool:
        """Determine if text needs translation."""
        if pd.isna(text) or not isinstance(text, str):
            return False
        
        text = str(text).strip()
        if not text:
            return False
        
        # Skip numeric values, dates, and pure English text
        if (self.numeric_pattern.match(text) or 
            self.date_pattern.match(text) or 
            self.english_pattern.match(text)):
            return False
        
        return self.contains_chinese(text)
    
    def get_text_hash(self, text: str) -> str:
        """Generate hash for text deduplication."""
        if pd.isna(text):
            return "null"
        return hashlib.md5(str(text).encode('utf-8')).hexdigest()

class ProgressTracker:
    """Tracks and displays real-time translation progress."""
    
    def __init__(self, total_unique_texts: int):
        self.total_unique_texts = total_unique_texts
        self.completed_translations = 0
        self.start_time = time.time()
        self.last_update_time = time.time()
        self.translations_since_last_update = 0
        self.logger = logging.getLogger(__name__)
    
    def update_progress(self, completed_count: int):
        """Update progress and display statistics."""
        self.completed_translations = completed_count
        current_time = time.time()
        
        # Calculate progress percentage
        progress_percent = (self.completed_translations / self.total_unique_texts) * 100
        
        # Calculate elapsed time and speed
        elapsed_time = current_time - self.start_time
        if elapsed_time > 0:
            overall_speed = self.completed_translations / elapsed_time * 60  # per minute
        else:
            overall_speed = 0
        
        # Calculate recent speed
        time_since_update = current_time - self.last_update_time
        if time_since_update > 0:
            recent_speed = self.translations_since_last_update / time_since_update * 60
        else:
            recent_speed = 0
        
        # Estimate remaining time
        if overall_speed > 0:
            remaining_texts = self.total_unique_texts - self.completed_translations
            estimated_remaining_minutes = remaining_texts / overall_speed
        else:
            estimated_remaining_minutes = 0
        
        # Display progress
        print(f"\n{'='*60}")
        print(f"TRANSLATION PROGRESS")
        print(f"{'='*60}")
        print(f"Unique texts identified: {self.total_unique_texts:,}")
        print(f"Completed translations: {self.completed_translations:,}")
        print(f"Progress: {progress_percent:.1f}%")
        print(f"Overall speed: {overall_speed:.1f} translations/minute")
        print(f"Recent speed: {recent_speed:.1f} translations/minute")
        print(f"Estimated time remaining: {estimated_remaining_minutes:.1f} minutes")
        print(f"Elapsed time: {elapsed_time/60:.1f} minutes")
        print(f"{'='*60}")
        
        # Log progress
        self.logger.info(f"Progress: {progress_percent:.1f}% ({self.completed_translations}/{self.total_unique_texts})")
        
        # Reset counters for next update
        self.last_update_time = current_time
        self.translations_since_last_update = 0
    
    def increment_translation(self):
        """Increment translation counter."""
        self.translations_since_last_update += 1

class AdvancedChineseTranslator:
    """Advanced Chinese to English translator with deduplication and checkpointing."""
    
    def __init__(self, config_file: str = CONFIG_FILE):
        """Initialize the translator with configuration."""
        self.logger = logging.getLogger(__name__)
        self.config = self._load_config(config_file)
        
        if not self.config:
            raise ValueError("Failed to load configuration")
        
        self.api_keys = self.config.get("api_keys", [])
        if not self.api_keys:
            raise ValueError("No API keys found in configuration")
        
        # Initialize API clients
        self.clients = [
            AsyncOpenAI(api_key=key, base_url="https://cloud.infini-ai.com/maas/v1/")
            for key in self.api_keys
        ]
        
        # Initialize components
        self.text_analyzer = ChineseTextAnalyzer()
        self.checkpoint_manager = TranslationCheckpoint()
        
        # Translation state
        self.translation_cache = {}
        self.unique_texts = {}  # hash -> original text
        self.text_mappings = defaultdict(list)  # hash -> [(row_idx, col_name), ...]
        self.disabled_api_keys = set()
        self.api_request_counter = 0
        
        self.logger.info(f"Initialized translator with {len(self.api_keys)} API keys")
    
    def _load_config(self, config_path: str) -> Dict:
        """Load configuration from JSON file."""
        config_path = Path(config_path)
        if not config_path.exists():
            self.logger.error(f"Configuration file not found: {config_path}")
            return {}
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            self.logger.info(f"Loaded configuration from {config_path}")
            return config
        except Exception as e:
            self.logger.error(f"Error loading configuration: {e}")
            return {}

    async def _translate_text(self, text: str, target_language: str = "English", client_idx: Optional[int] = None) -> str:
        """Translate a single text using the API."""
        if pd.isna(text) or not isinstance(text, str):
            return str(text) if not pd.isna(text) else ""

        text_str = str(text).strip()
        if not text_str:
            return text_str

        # Check if text needs translation
        if not self.text_analyzer.is_translatable(text_str):
            return text_str

        # Check cache first
        cache_key = f"{text_str}_{target_language}"
        if cache_key in self.translation_cache:
            return self.translation_cache[cache_key]

        # Select available client
        available_clients = [i for i in range(len(self.clients)) if i not in self.disabled_api_keys]
        if not available_clients:
            self.logger.error("No available API clients")
            return text_str

        if client_idx is not None and client_idx not in self.disabled_api_keys:
            client = self.clients[client_idx]
        else:
            client_idx = available_clients[self.api_request_counter % len(available_clients)]
            client = self.clients[client_idx]
            self.api_request_counter += 1

        # Prepare translation request
        system_message = f"""You are a professional translator specializing in business and legal documents.
Translate the given Chinese text to {target_language} accurately while preserving the original meaning and context.
For business terms, company types, and legal entities, use standard business terminology.
Return only the translated text without any additional explanation."""

        user_message = f"""Please translate the following Chinese text to {target_language}:

{text_str}

Translation:"""

        # Attempt translation with retries
        for attempt in range(MAX_RETRIES):
            try:
                response = await client.chat.completions.create(
                    model="deepseek-r1",
                    messages=[
                        {"role": "system", "content": system_message},
                        {"role": "user", "content": user_message}
                    ],
                    temperature=0.1,
                    max_tokens=1000
                )

                if hasattr(response, 'choices') and len(response.choices) > 0:
                    translated_text = response.choices[0].message.content.strip()
                    # Clean up the response
                    if translated_text.startswith("Translation:"):
                        translated_text = translated_text[12:].strip()

                    # Cache the translation
                    self.translation_cache[cache_key] = translated_text
                    return translated_text
                else:
                    self.logger.warning(f"Invalid API response for text: {text_str[:50]}...")

            except Exception as e:
                error_str = str(e)
                self.logger.error(f"Translation error (attempt {attempt + 1}): {error_str}")

                # Handle rate limiting
                if "429" in error_str or "RPD exceeded" in error_str:
                    self.logger.warning(f"API key {client_idx} hit rate limit, disabling")
                    self.disabled_api_keys.add(client_idx)

                    # Try with different client
                    available_clients = [i for i in range(len(self.clients)) if i not in self.disabled_api_keys]
                    if available_clients:
                        await asyncio.sleep(2)
                        client_idx = available_clients[0]
                        client = self.clients[client_idx]
                        continue

                if attempt < MAX_RETRIES - 1:
                    await asyncio.sleep(2 ** attempt)  # Exponential backoff

        # Return original text if all attempts failed
        self.logger.warning(f"Failed to translate after {MAX_RETRIES} attempts: {text_str[:50]}...")
        return text_str

    def analyze_dataframe(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze dataframe to identify unique Chinese texts for translation."""
        self.logger.info("Analyzing dataframe for Chinese text...")

        unique_texts = {}
        text_mappings = defaultdict(list)
        total_cells = 0
        chinese_cells = 0

        for column in df.columns:
            for row_idx, value in enumerate(df[column]):
                total_cells += 1

                if self.text_analyzer.contains_chinese(str(value)):
                    chinese_cells += 1

                    if self.text_analyzer.is_translatable(str(value)):
                        text_hash = self.text_analyzer.get_text_hash(str(value))
                        unique_texts[text_hash] = str(value)
                        text_mappings[text_hash].append((row_idx, column))

        analysis_result = {
            'total_cells': total_cells,
            'chinese_cells': chinese_cells,
            'unique_translatable_texts': len(unique_texts),
            'unique_texts': unique_texts,
            'text_mappings': text_mappings,
            'deduplication_ratio': chinese_cells / len(unique_texts) if unique_texts else 0
        }

        self.logger.info(f"Analysis complete:")
        self.logger.info(f"  Total cells: {total_cells:,}")
        self.logger.info(f"  Chinese cells: {chinese_cells:,}")
        self.logger.info(f"  Unique translatable texts: {len(unique_texts):,}")
        self.logger.info(f"  Deduplication ratio: {analysis_result['deduplication_ratio']:.2f}x")

        return analysis_result

    async def translate_unique_texts(self, unique_texts: Dict[str, str],
                                   checkpoint_name: str) -> Dict[str, str]:
        """Translate unique texts with checkpointing and progress tracking."""
        self.logger.info(f"Starting translation of {len(unique_texts)} unique texts...")

        # Check for existing checkpoint
        checkpoint_data = self.checkpoint_manager.load_checkpoint(checkpoint_name)
        if checkpoint_data:
            completed_translations = checkpoint_data.get('completed_translations', {})
            self.logger.info(f"Resuming from checkpoint with {len(completed_translations)} completed translations")
        else:
            completed_translations = {}

        # Initialize progress tracker
        progress_tracker = ProgressTracker(len(unique_texts))
        progress_tracker.update_progress(len(completed_translations))

        # Prepare texts for translation
        remaining_texts = {k: v for k, v in unique_texts.items() if k not in completed_translations}
        text_hashes = list(remaining_texts.keys())

        # Process in batches
        for i in range(0, len(text_hashes), BATCH_SIZE):
            batch_hashes = text_hashes[i:i + BATCH_SIZE]
            batch_texts = [remaining_texts[h] for h in batch_hashes]

            # Translate batch concurrently
            translation_tasks = [
                self._translate_text(text, "English")
                for text in batch_texts
            ]

            try:
                batch_translations = await asyncio.gather(*translation_tasks, return_exceptions=True)

                # Process results
                for text_hash, translation in zip(batch_hashes, batch_translations):
                    if isinstance(translation, Exception):
                        self.logger.error(f"Translation failed for hash {text_hash}: {translation}")
                        translation = remaining_texts[text_hash]  # Use original text

                    completed_translations[text_hash] = translation
                    progress_tracker.increment_translation()

                # Update progress
                if len(completed_translations) % PROGRESS_UPDATE_INTERVAL == 0:
                    progress_tracker.update_progress(len(completed_translations))

                # Save checkpoint
                if len(completed_translations) % CHECKPOINT_INTERVAL == 0:
                    checkpoint_data = {
                        'completed_translations': completed_translations,
                        'timestamp': datetime.now().isoformat(),
                        'total_unique_texts': len(unique_texts)
                    }
                    self.checkpoint_manager.save_checkpoint(checkpoint_data, checkpoint_name)

            except Exception as e:
                self.logger.error(f"Batch translation error: {e}")
                continue

        # Final progress update
        progress_tracker.update_progress(len(completed_translations))

        # Save final checkpoint
        final_checkpoint_data = {
            'completed_translations': completed_translations,
            'timestamp': datetime.now().isoformat(),
            'total_unique_texts': len(unique_texts),
            'status': 'completed'
        }
        self.checkpoint_manager.save_checkpoint(final_checkpoint_data, checkpoint_name)

        self.logger.info(f"Translation completed: {len(completed_translations)} texts translated")
        return completed_translations

    def apply_translations(self, df: pd.DataFrame, text_mappings: Dict[str, List[Tuple]],
                          translations: Dict[str, str]) -> pd.DataFrame:
        """Apply translations back to the dataframe using mappings."""
        self.logger.info("Applying translations to dataframe...")

        # Create a copy of the dataframe
        translated_df = df.copy()

        # Apply translations using mappings
        for text_hash, positions in text_mappings.items():
            if text_hash in translations:
                translated_text = translations[text_hash]
                for row_idx, column in positions:
                    translated_df.iloc[row_idx, translated_df.columns.get_loc(column)] = translated_text

        self.logger.info("Translations applied successfully")
        return translated_df

    def create_multilingual_excel(self, original_df: pd.DataFrame, english_df: pd.DataFrame,
                                 analysis_result: Dict[str, Any], output_filename: str = None) -> str:
        """Create multilingual Excel file with separate sheets."""
        if output_filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_filename = f"chinese_enterprise_translated_{timestamp}.xlsx"

        self.logger.info(f"Creating multilingual Excel file: {output_filename}")

        try:
            with pd.ExcelWriter(output_filename, engine='openpyxl') as writer:
                # Original Chinese data
                original_df.to_excel(writer, sheet_name='Original_Chinese', index=False)

                # English translations
                english_df.to_excel(writer, sheet_name='English_Translation', index=False)

                # Progress tracking and statistics
                stats_data = {
                    'Metric': [
                        'Total Cells',
                        'Chinese Cells',
                        'Unique Translatable Texts',
                        'Deduplication Ratio',
                        'Translation Efficiency',
                        'Processing Date',
                        'Source File'
                    ],
                    'Value': [
                        f"{analysis_result['total_cells']:,}",
                        f"{analysis_result['chinese_cells']:,}",
                        f"{analysis_result['unique_translatable_texts']:,}",
                        f"{analysis_result['deduplication_ratio']:.2f}x",
                        f"{(1 - analysis_result['unique_translatable_texts'] / analysis_result['chinese_cells']) * 100:.1f}%" if analysis_result['chinese_cells'] > 0 else "N/A",
                        datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                        "chinese_enterprise_50_sample_english_20250701_014513.csv"
                    ]
                }

                stats_df = pd.DataFrame(stats_data)
                stats_df.to_excel(writer, sheet_name='Translation_Statistics', index=False)

                # Translation cache summary (top 20 most common translations)
                if self.translation_cache:
                    cache_items = list(self.translation_cache.items())
                    cache_summary = []

                    for cache_key, translation in cache_items[:20]:
                        original_text = cache_key.split('_English')[0] if '_English' in cache_key else cache_key
                        cache_summary.append({
                            'Original_Chinese': original_text[:100],  # Truncate long text
                            'English_Translation': translation[:100]
                        })

                    if cache_summary:
                        cache_df = pd.DataFrame(cache_summary)
                        cache_df.to_excel(writer, sheet_name='Sample_Translations', index=False)

            self.logger.info(f"Excel file created successfully: {output_filename}")
            return output_filename

        except Exception as e:
            self.logger.error(f"Error creating Excel file: {e}")
            raise

    async def translate_csv_file(self, input_file: str, output_file: str = None,
                               resume_checkpoint: str = None) -> str:
        """Main method to translate a CSV file with all optimizations."""
        self.logger.info(f"Starting translation of file: {input_file}")

        # Load CSV file
        try:
            df = pd.read_csv(input_file, encoding='utf-8-sig')
            self.logger.info(f"Loaded {len(df)} records with {len(df.columns)} columns")
        except Exception as e:
            self.logger.error(f"Error loading CSV file: {e}")
            raise

        # Analyze dataframe for Chinese text
        analysis_result = self.analyze_dataframe(df)

        if analysis_result['unique_translatable_texts'] == 0:
            self.logger.warning("No Chinese text found for translation")
            return input_file

        # Generate checkpoint name
        if resume_checkpoint is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            checkpoint_name = f"translation_{timestamp}"
        else:
            checkpoint_name = resume_checkpoint

        # Translate unique texts
        translations = await self.translate_unique_texts(
            analysis_result['unique_texts'],
            checkpoint_name
        )

        # Apply translations to dataframe
        english_df = self.apply_translations(
            df,
            analysis_result['text_mappings'],
            translations
        )

        # Create multilingual Excel output
        excel_filename = self.create_multilingual_excel(
            df,
            english_df,
            analysis_result,
            output_file
        )

        return excel_filename

async def main():
    """Main function to execute the translation process."""
    # Setup logging
    log_file = setup_logging()
    logger = logging.getLogger(__name__)

    try:
        # Configuration
        input_file = "chinese_enterprise_50_sample_english_20250701_014513.csv"

        # Check if input file exists
        if not os.path.exists(input_file):
            logger.error(f"Input file not found: {input_file}")
            print(f"Error: Input file '{input_file}' not found!")
            return

        # Check if config file exists
        if not os.path.exists(CONFIG_FILE):
            logger.error(f"Configuration file not found: {CONFIG_FILE}")
            print(f"Error: Configuration file '{CONFIG_FILE}' not found!")
            print("Please ensure infini_config.json exists with valid API keys")
            return

        # Initialize translator
        logger.info("Initializing advanced Chinese translator...")
        translator = AdvancedChineseTranslator()

        # Check for existing checkpoints
        checkpoints = translator.checkpoint_manager.list_checkpoints()
        resume_checkpoint = None

        if checkpoints:
            print(f"\nFound {len(checkpoints)} existing checkpoints:")
            for i, checkpoint in enumerate(checkpoints):
                print(f"  {i+1}. {checkpoint}")

            choice = input("\nDo you want to resume from an existing checkpoint? (y/n): ").lower()
            if choice == 'y':
                try:
                    checkpoint_idx = int(input("Enter checkpoint number: ")) - 1
                    if 0 <= checkpoint_idx < len(checkpoints):
                        resume_checkpoint = checkpoints[checkpoint_idx]
                        logger.info(f"Will resume from checkpoint: {resume_checkpoint}")
                except (ValueError, IndexError):
                    logger.warning("Invalid checkpoint selection, starting fresh")

        # Execute translation
        print(f"\n{'='*60}")
        print("STARTING ADVANCED CHINESE TRANSLATION")
        print(f"{'='*60}")
        print(f"Input file: {input_file}")
        print(f"Configuration: {CONFIG_FILE}")
        print(f"Log file: {log_file}")
        if resume_checkpoint:
            print(f"Resuming from: {resume_checkpoint}")
        print(f"{'='*60}")

        # Translate the file
        output_file = await translator.translate_csv_file(
            input_file,
            resume_checkpoint=resume_checkpoint
        )

        # Display final summary
        print(f"\n{'='*60}")
        print("TRANSLATION COMPLETED SUCCESSFULLY!")
        print(f"{'='*60}")
        print(f"Input file: {input_file}")
        print(f"Output file: {output_file}")
        print(f"Log file: {log_file}")
        print(f"Checkpoints saved in: checkpoints/")
        print(f"{'='*60}")

        logger.info("Translation process completed successfully!")

    except Exception as e:
        logger.error(f"Error in main process: {e}")
        print(f"\nError: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(main())
