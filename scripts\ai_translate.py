#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import time
import json
import requests
import os
from typing import List, Dict

def ai_batch_translate():
    """
    使用AI分批翻译CSV文件
    """
    print("开始AI分批翻译...")
    
    # 读取原始中文文件
    df = pd.read_csv("GB_T_4754_2017_converted.csv", encoding='utf-8')
    print(f"读取到 {len(df)} 行数据")
    
    # 创建结果DataFrame
    result_data = []
    
    # 分批处理，每批50行
    batch_size = 50
    total_batches = (len(df) + batch_size - 1) // batch_size
    
    for i in range(total_batches):
        start_idx = i * batch_size
        end_idx = min((i + 1) * batch_size, len(df))
        batch_df = df.iloc[start_idx:end_idx]
        
        print(f"处理批次 {i+1}/{total_batches} (行 {start_idx+1}-{end_idx})")
        
        # 翻译当前批次
        translated_batch = translate_batch(batch_df)
        result_data.extend(translated_batch)
        
        # 避免请求过快，稍微延迟
        time.sleep(1)
    
    # 创建结果DataFrame
    result_df = pd.DataFrame(result_data, columns=[
        'Classification Level', 'Classification Code', 'Classification Name', 
        'Parent Code', 'Core Keywords', 'Description'
    ])
    
    # 保存结果
    output_file = "GB_T_4754_2017_converted_english_AI.csv"
    result_df.to_csv(output_file, index=False, encoding='utf-8')
    print(f"AI翻译完成！已保存到 {output_file}")
    print(f"共处理 {len(result_df)} 行数据")
    
    return output_file

def translate_batch(batch_df) -> List[List[str]]:
    """
    翻译一个批次的数据
    """
    result = []
    
    for idx, row in batch_df.iterrows():
        # 提取各列数据
        classification_level = str(row.iloc[0]) if pd.notna(row.iloc[0]) else ""
        classification_code = str(row.iloc[1]) if pd.notna(row.iloc[1]) else ""
        classification_name = str(row.iloc[2]) if pd.notna(row.iloc[2]) else ""
        parent_code = str(row.iloc[3]) if pd.notna(row.iloc[3]) else ""
        core_keywords = str(row.iloc[4]) if pd.notna(row.iloc[4]) else ""
        description = str(row.iloc[5]) if pd.notna(row.iloc[5]) else ""
        
        # 翻译各字段
        translated_row = [
            translate_classification_level(classification_level),
            classification_code,  # 代码保持不变
            translate_text_field(classification_name, "industry classification name"),
            parent_code,  # 代码保持不变
            translate_keywords_field(core_keywords),
            translate_text_field(description, "description")
        ]
        
        result.append(translated_row)
    
    return result

def translate_classification_level(text: str) -> str:
    """
    翻译分类级别
    """
    if not text or text == 'nan':
        return ""
    
    text = text.strip()
    
    # 简单映射
    mappings = {
        '分类级别': 'Classification Level',
        '门类': 'Sector',
        '大类': 'Major Category',
        '中类': 'Medium Category',
        '小类': 'Minor Category'
    }
    
    return mappings.get(text, text)

def translate_keywords_field(text: str) -> str:
    """
    翻译关键词字段
    """
    if not text or text == 'nan':
        return ""
    
    # 如果包含分号，分别翻译
    if ';' in text:
        keywords = text.split(';')
        translated_keywords = []
        for keyword in keywords:
            keyword = keyword.strip()
            if keyword:
                translated = translate_text_field(keyword, "keyword")
                if translated:
                    translated_keywords.append(translated)
        return ';'.join(translated_keywords)
    else:
        return translate_text_field(text, "keyword")

def translate_text_field(text: str, field_type: str) -> str:
    """
    使用简化的翻译逻辑翻译文本字段
    """
    if not text or text == 'nan':
        return ""
    
    text = text.strip()
    
    # 如果已经是英文，直接返回
    if is_mostly_english(text):
        return text
    
    # 使用内置翻译词典
    return translate_with_builtin_dict(text)

def is_mostly_english(text: str) -> bool:
    """
    判断文本是否主要是英文
    """
    if not text:
        return True
    
    # 计算英文字符比例
    english_chars = sum(1 for c in text if c.isascii())
    total_chars = len(text.replace(' ', '').replace(',', '').replace(';', ''))
    
    if total_chars == 0:
        return True
    
    english_ratio = english_chars / total_chars
    return english_ratio > 0.7

def translate_with_builtin_dict(text: str) -> str:
    """
    使用内置词典翻译
    """
    # 扩展的翻译词典
    translation_dict = {
        # 表头和基础
        '分类级别': 'Classification Level',
        '分类代码': 'Classification Code',
        '分类名称': 'Classification Name',
        '上级代码': 'Parent Code',
        '核心关键词': 'Core Keywords',
        '说明': 'Description',
        
        # 门类翻译
        '农、林、牧、渔业': 'Agriculture, Forestry, Animal Husbandry and Fishery',
        '采矿业': 'Mining',
        '制造业': 'Manufacturing',
        '电力、热力、燃气及水生产和供应业': 'Production and Supply of Electric Power, Heat, Gas and Water',
        '建筑业': 'Construction',
        '批发和零售业': 'Wholesale and Retail Trade',
        '交通运输、仓储和邮政业': 'Transport, Storage and Postal Services',
        '住宿和餐饮业': 'Accommodation and Food Services',
        '信息传输、软件和信息技术服务业': 'Information Transmission, Software and Information Technology Services',
        '金融业': 'Financial Intermediation',
        '房地产业': 'Real Estate Activities',
        '租赁和商务服务业': 'Leasing and Business Activities',
        '科学研究和技术服务业': 'Scientific Research and Technical Services',
        '水利、环境和公共设施管理业': 'Water Conservancy, Environment and Public Facilities Management',
        '居民服务、修理和其他服务业': 'Resident Services, Repair and Other Services',
        '教育': 'Education',
        '卫生和社会工作': 'Health and Social Work',
        '文化、体育和娱乐业': 'Culture, Sports and Entertainment',
        '公共管理、社会保障和社会组织': 'Public Administration, Social Security and Social Organizations',
        '国际组织': 'International Organizations',
        
        # 农业细分
        '农业': 'Agriculture',
        '林业': 'Forestry',
        '畜牧业': 'Animal Husbandry',
        '渔业': 'Fishery',
        '农、林、牧、渔专业及辅助性活动': 'Professional and Support Activities for Agriculture, Forestry, Animal Husbandry and Fishery',
        
        # 农业种植
        '谷物种植': 'Grain Cultivation',
        '稻谷种植': 'Rice Cultivation',
        '小麦种植': 'Wheat Cultivation',
        '玉米种植': 'Corn Cultivation',
        '其他谷物种植': 'Other Grain Cultivation',
        '豆类、油料和薯类种植': 'Legume, Oil Crop and Tuber Cultivation',
        '豆类种植': 'Legume Cultivation',
        '油料种植': 'Oil Crop Cultivation',
        '薯类种植': 'Tuber Cultivation',
        '棉、麻、糖、烟草种植': 'Cotton, Hemp, Sugar and Tobacco Cultivation',
        '棉花种植': 'Cotton Cultivation',
        '麻类种植': 'Hemp Cultivation',
        '糖料种植': 'Sugar Crop Cultivation',
        '烟草种植': 'Tobacco Cultivation',
        '蔬菜、食用菌及园艺作物种植': 'Vegetable, Edible Fungi and Horticultural Crop Cultivation',
        '蔬菜种植': 'Vegetable Cultivation',
        '食用菌种植': 'Edible Fungi Cultivation',
        '花卉种植': 'Flower Cultivation',
        '其他园艺作物种植': 'Other Horticultural Crop Cultivation',
        '水果种植': 'Fruit Cultivation',
        '仁果类和核果类水果种植': 'Pome and Stone Fruit Cultivation',
        '葡萄种植': 'Grape Cultivation',
        '柑橘类种植': 'Citrus Cultivation',
        '香蕉等亚热带水果种植': 'Banana and Other Subtropical Fruit Cultivation',
        '其他水果种植': 'Other Fruit Cultivation',
        '坚果、含油果、香料和饮料作物种植': 'Nut, Oil-bearing Fruit, Spice and Beverage Crop Cultivation',
        '坚果种植': 'Nut Cultivation',
        '含油果种植': 'Oil-bearing Fruit Cultivation',
        '香料作物种植': 'Spice Crop Cultivation',
        '茶叶种植': 'Tea Cultivation',
        '其他饮料作物种植': 'Other Beverage Crop Cultivation',
        '中药材种植': 'Traditional Chinese Medicine Material Cultivation',
        '中草药种植': 'Traditional Chinese Herbal Medicine Cultivation',
        '其他中药材种植': 'Other Traditional Chinese Medicine Material Cultivation',
        '草种植及割草': 'Grass Cultivation and Cutting',
        '草种植': 'Grass Cultivation',
        '天然草原割草': 'Natural Grassland Cutting',
        '其他农业': 'Other Agriculture',
        
        # 林业
        '林木育种和育苗': 'Forest Tree Breeding and Seedling Raising',
        '林木育种': 'Forest Tree Breeding',
        '林木育苗': 'Forest Tree Seedling Raising',
        '造林和更新': 'Afforestation and Renewal',
        '森林经营、管护和改培': 'Forest Management, Protection and Transformation',
        '森林经营和管护': 'Forest Management and Protection',
        '森林改培': 'Forest Transformation',
        '木材和竹材采运': 'Wood and Bamboo Harvesting and Transportation',
        '木材采运': 'Wood Harvesting and Transportation',
        '竹材采运': 'Bamboo Harvesting and Transportation',
        '林产品采集': 'Forest Product Collection',
        '木竹材林产品采集': 'Wood and Bamboo Forest Product Collection',
        '非木竹材林产品采集': 'Non-wood and Non-bamboo Forest Product Collection',
        
        # 畜牧业
        '牲畜饲养': 'Livestock Raising',
        '牛的饲养': 'Cattle Raising',
        '马的饲养': 'Horse Raising',
        '猪的饲养': 'Pig Raising',
        '羊的饲养': 'Sheep Raising',
        '骆驼饲养': 'Camel Raising',
        '其他牲畜饲养': 'Other Livestock Raising',
        '家禽饲养': 'Poultry Raising',
        '鸡的饲养': 'Chicken Raising',
        '鸭的饲养': 'Duck Raising',
        '鹅的饲养': 'Goose Raising',
        '其他家禽饲养': 'Other Poultry Raising',
        '狩猎和捕捉动物': 'Hunting and Animal Capturing',
        '其他畜牧业': 'Other Animal Husbandry',
        '兔的饲养': 'Rabbit Raising',
        '蜜蜂饲养': 'Bee Raising',
        '其他未列明畜牧业': 'Other Animal Husbandry Not Elsewhere Classified',
        
        # 渔业
        '水产养殖': 'Aquaculture',
        '海水养殖': 'Marine Aquaculture',
        '内陆养殖': 'Inland Aquaculture',
        '水产捕捞': 'Fishing',
        '海水捕捞': 'Marine Fishing',
        '内陆捕捞': 'Inland Fishing',
        
        # 专业及辅助性活动
        '农业专业及辅助性活动': 'Professional and Support Activities for Agriculture',
        '种子种苗培育活动': 'Seed and Seedling Cultivation Activities',
        '农业机械活动': 'Agricultural Machinery Activities',
        '灌溉活动': 'Irrigation Activities',
        '农产品初加工活动': 'Primary Processing Activities for Agricultural Products',
        '农作物病虫害防治活动': 'Crop Pest and Disease Control Activities',
        '其他农业专业及辅助性活动': 'Other Professional and Support Activities for Agriculture',
        '林业专业及辅助性活动': 'Professional and Support Activities for Forestry',
        '林业有害生物防治活动': 'Forestry Pest Control Activities',
        '森林防火活动': 'Forest Fire Prevention Activities',
        '林产品初级加工活动': 'Primary Processing Activities for Forest Products',
        '其他林业专业及辅助性活动': 'Other Professional and Support Activities for Forestry',
        '畜牧专业及辅助性活动': 'Professional and Support Activities for Animal Husbandry',
        '畜牧良种繁殖活动': 'Livestock Breeding Activities',
        '畜禽粪污处理活动': 'Livestock and Poultry Manure Treatment Activities',
        '其他畜牧专业及辅助性活动': 'Other Professional and Support Activities for Animal Husbandry',
        '渔业专业及辅助性活动': 'Professional and Support Activities for Fishery',
        '鱼苗及鱼种场活动': 'Fish Fry and Fish Seed Farm Activities',
        '其他渔业专业及辅助性活动': 'Other Professional and Support Activities for Fishery',
        
        # 关键词翻译
        '农业生产': 'agricultural production',
        '种植业': 'planting industry',
        '畜牧业': 'animal husbandry',
        '林业': 'forestry',
        '渔业': 'fishery',
        '农产品': 'agricultural products',
        '农业': 'agriculture',
        '农作物': 'crops',
        '作物生产': 'crop production',
        '谷物种植': 'grain cultivation',
        '豆类': 'legumes',
        '油料': 'oil crops',
        '薯类种植': 'tuber cultivation',
        '棉': 'cotton',
        '麻': 'hemp',
        '糖': 'sugar',
        '烟草种植': 'tobacco cultivation',
        '蔬菜': 'vegetables',
        '食用菌': 'edible fungi',
        '园艺作物种植': 'horticultural crop cultivation',
        '水果种植': 'fruit cultivation',
        '仁果类': 'pome fruits',
        '核果类水果种植': 'stone fruit cultivation',
        '葡萄种植': 'grape cultivation',
        '柑橘类种植': 'citrus cultivation',
        '香蕉等亚热带水果种植': 'banana and other subtropical fruit cultivation',
        '其他水果种植': 'other fruit cultivation',
        '坚果': 'nuts',
        '含油果': 'oil-bearing fruits',
        '香料作物种植': 'spice crop cultivation',
        '饮料作物种植': 'beverage crop cultivation',
        '茶叶种植': 'tea cultivation',
        '其他饮料作物种植': 'other beverage crop cultivation',
        '中药材种植': 'traditional Chinese medicine material cultivation',
        '中草药种植': 'traditional Chinese herbal medicine cultivation',
        '其他中药材种植': 'other traditional Chinese medicine material cultivation',
        '草种植': 'grass cultivation',
        '割草': 'grass cutting',
        '天然草原割草': 'natural grassland cutting',
        '其他农业': 'other agriculture',
        '林木育种': 'forest tree breeding',
        '育苗': 'seedling raising',
        '造林': 'afforestation',
        '更新': 'renewal',
        '森林经营': 'forest management',
        '管护': 'protection',
        '改培': 'transformation',
        '木材': 'wood',
        '竹材采运': 'bamboo harvesting and transportation',
        '林产品采集': 'forest product collection',
        '木竹材林产品采集': 'wood and bamboo forest product collection',
        '非木竹材林产品采集': 'non-wood and non-bamboo forest product collection',
        '养殖业': 'breeding industry',
        '动物饲养': 'animal raising',
        '牲畜饲养': 'livestock raising',
        '牛的饲养': 'cattle raising',
        '马的饲养': 'horse raising',
        '猪的饲养': 'pig raising',
        '羊的饲养': 'sheep raising',
        '骆驼饲养': 'camel raising',
        '其他牲畜饲养': 'other livestock raising',
        '家禽饲养': 'poultry raising',
        '鸡的饲养': 'chicken raising',
        '鸭的饲养': 'duck raising',
        '鹅的饲养': 'goose raising',
        '其他家禽饲养': 'other poultry raising',
        '狩猎': 'hunting',
        '捕捉动物': 'animal capturing',
        '其他畜牧业': 'other animal husbandry',
        '兔的饲养': 'rabbit raising',
        '蜜蜂饲养': 'bee raising',
        '其他未列明畜牧业': 'other animal husbandry not elsewhere classified',
        '水产养殖': 'aquaculture',
        '海水养殖': 'marine aquaculture',
        '内陆养殖': 'inland aquaculture',
        '水产捕捞': 'fishing',
        '海水捕捞': 'marine fishing',
        '内陆捕捞': 'inland fishing',
        
        # 常用短语
        '指对各种农作物的种植': 'refers to the cultivation of various crops',
        '指以收获籽实为主的农作物的种植，包括稻谷、小麦、玉米等农作物的种植和作为饲料和工业原料的谷物的种植': 'refers to the cultivation of crops mainly for harvesting seeds, including rice, wheat, corn and other crops, and grains used as feed and industrial raw materials',
        '指用于制糖的甘蔗和甜菜的种植': 'refers to the cultivation of sugarcane and sugar beet for sugar production',
        '指苹果、梨、桃、杏、李子等水果种植': 'refers to the cultivation of fruits such as apples, pears, peaches, apricots and plums',
        '指香蕉、菠萝、芒果等亚热带水果种植': 'refers to the cultivation of subtropical fruits such as bananas, pineapples and mangoes',
        '指油茶、橄榄、油棕榈、油桐籽、椰子等种植': 'refers to the cultivation of oil tea, olives, oil palms, tung seeds, coconuts, etc.',
        '指主要用于中药配制以及中成药加工的药材作物的种植': 'refers to the cultivation of medicinal crops mainly used for traditional Chinese medicine preparation and processing',
        '指主要用于中药配制以及中成药加工的各种中草药材作物的种植': 'refers to the cultivation of various traditional Chinese herbal medicine crops mainly used for traditional Chinese medicine preparation and processing',
        '指人工种植收获牧草': 'refers to artificial cultivation and harvesting of pasture grass',
        '指天然草原刈割收获牧草': 'refers to cutting and harvesting pasture grass from natural grasslands',
        '本门类包括': 'this sector includes',
        '本类包括': 'this category includes',
        '指对': 'refers to',
        '指以': 'refers to',
        '指用于': 'refers to',
        '指在': 'refers to',
        '指为': 'refers to',
        '包括': 'including',
        '以及': 'and',
        '等': 'etc.',
        '各种': 'various',
        '其他': 'other',
        '未列明': 'not elsewhere classified',
        '活动': 'activities',
        '生产': 'production',
        '加工': 'processing',
        '制造': 'manufacturing',
        '种植': 'cultivation',
        '饲养': 'raising',
        '养殖': 'breeding',
        '捕捞': 'fishing',
        '采集': 'collection',
        '开采': 'mining',
        '服务': 'services',
        '管理': 'management',
        '经营': 'operation',
        
        # 数字修正
        '01～05': '01-05',
        '06～12': '06-12',
        '13～43': '13-43',
        '～': '-',
        '、': ', ',
        '，': ', ',
        '。': '. ',
        '；': '; ',
        '：': ': ',
        '（': ' (',
        '）': ') ',
        '和': ' and ',
        '与': ' and ',
        '及': ' and ',
        '或': ' or ',
        '的': '',
        '了': '',
        '在': 'in',
        '为': 'for',
        '对': 'to',
        '从': 'from',
        '至': 'to',
        '等等': 'etc.',
    }
    
    # 应用翻译
    result = text
    for chinese, english in sorted(translation_dict.items(), key=lambda x: len(x[0]), reverse=True):
        if chinese in result:
            result = result.replace(chinese, english)
    
    # 清理多余空格
    import re
    result = re.sub(r'\s+', ' ', result)
    result = result.strip()
    
    return result

if __name__ == "__main__":
    ai_batch_translate()