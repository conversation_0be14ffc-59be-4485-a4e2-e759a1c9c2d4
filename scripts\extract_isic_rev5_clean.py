import pandas as pd
import os
import re

def extract_isic_rev5_data():
    """
    Extract ISIC Rev.5 classification data and convert to English format
    """
    
    # Check ISIC file paths
    isic_paths = [
        'raw_data/ISIC5_Exp_Notes_11Mar2024.xlsx',
        'final_data/ISIC_Rev5/ISIC5_Exp_Notes_11Mar2024.xlsx'
    ]
    
    isic_file = None
    for path in isic_paths:
        if os.path.exists(path):
            isic_file = path
            break
    
    if not isic_file:
        print("ISIC5_Exp_Notes_11Mar2024.xlsx file not found")
        return
    
    print(f"Reading file: {isic_file}")
    
    # Read data
    df = pd.read_excel(isic_file, sheet_name='ISIC5')
    print(f"Data shape: {df.shape}")
    
    # Determine classification level based on ISIC code length
    def get_classification_level(code):
        if pd.isna(code):
            return None
        code_str = str(code).strip()
        if len(code_str) == 1:
            return "Section"  # Section (A, B, C, etc.)
        elif len(code_str) == 3:
            return "Division"  # Division (A01, A02, etc.)
        elif len(code_str) == 4:
            return "Group"  # Group (A011, A012, etc.)
        elif len(code_str) == 5:
            return "Class"  # Class (A0111, A0112, etc.)
        else:
            return "Other"
    
    def get_parent_code(code):
        if pd.isna(code):
            return ""
        code_str = str(code).strip()
        if len(code_str) <= 1:
            return ""
        else:
            return code_str[:-1]
    
    def clean_description(text):
        """Clean description text"""
        if pd.isna(text):
            return ""
        text = str(text)
        # Remove special encoding characters
        text = re.sub(r'_x[0-9A-Fa-f]+_', '', text)
        # Replace newlines and tabs with spaces
        text = re.sub(r'[\r\n\t]+', ' ', text)
        # Clean up multiple spaces
        text = re.sub(r'\s+', ' ', text)
        # Remove leading/trailing whitespace
        text = text.strip()
        return text
    
    def extract_keywords(title):
        """Extract keywords from title"""
        if pd.isna(title):
            return ""
        title = str(title)
        # Split by common separators and clean
        keywords = re.split(r'[,;]', title)
        keywords = [kw.strip() for kw in keywords if kw.strip()]
        return '; '.join(keywords)
    
    # Column mapping
    code_col = 'ISIC Rev 5 Code (with Section)'
    title_col = 'ISIC Rev 5 Title'
    desc_col = 'ISIC Rev 5 Includes'
    
    result_data = []
    
    # Process data
    for _, row in df.iterrows():
        code = row[code_col]
        title = row[title_col]
        description = row[desc_col] if desc_col in df.columns else ""
        
        if pd.notna(code) and pd.notna(title):
            level = get_classification_level(code)
            parent_code = get_parent_code(code)
            keywords = extract_keywords(title)
            clean_desc = clean_description(description)
            
            result_data.append({
                'Classification Level': level,
                'Classification Code': str(code).strip(),
                'Classification Name': str(title).strip(),
                'Parent Code': parent_code,
                'Core Keywords': keywords,
                'Description': clean_desc
            })
    
    # Create result DataFrame
    result_df = pd.DataFrame(result_data)
    
    # Ensure output directory exists
    output_dir = 'final_data/ISIC_Rev5'
    os.makedirs(output_dir, exist_ok=True)
    
    # Save result
    output_file = f'{output_dir}/ISIC_Rev5_converted.csv'
    try:
        result_df.to_csv(output_file, index=False, encoding='utf-8-sig')
    except PermissionError:
        # If file is locked, save with timestamp
        import datetime
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f'{output_dir}/ISIC_Rev5_converted_{timestamp}.csv'
        result_df.to_csv(output_file, index=False, encoding='utf-8-sig')
    
    print(f"\nConversion completed!")
    print(f"Output file: {output_file}")
    print(f"Total processed records: {len(result_df)}")
    
    # Statistics by classification level
    level_counts = result_df['Classification Level'].value_counts()
    print(f"\nLevel statistics:")
    for level, count in level_counts.items():
        print(f"  {level}: {count} records")
    
    print(f"\nSample records:")
    for _, row in result_df.head(5).iterrows():
        print(f"  {row['Classification Level']}: {row['Classification Code']} - {row['Classification Name']}")
    
    return result_df

if __name__ == "__main__":
    extract_isic_rev5_data()
