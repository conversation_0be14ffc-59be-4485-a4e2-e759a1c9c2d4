# Industry Classification Coding Rules Design Workflow Documentation

## Project Overview
This project aims to design industry classification coding rules based on Chinese government general classification standards (GB/T 4754-2017) and international standards (ISIC Rev.5), creating a complete code book with at least two-level classifications and defining core matching keywords for each sub-industry.

## Workflow Process

### 1. Data Collection Phase

#### 1.1 Chinese Government Classification Standards Collection
- **Data Source**: National Bureau of Statistics Official Website
- **Main Document**: "National Economic Industry Classification" (GB/T 4754-2017)
- **Download URL**: https://www.stats.gov.cn/xxgk/tjbz/gjtjbz/201710/t20171017_1758922.html
- **Download Time**: July 8, 2025
- **Standard Features**:
  - 20 major categories (sections)
  - 97 large categories (divisions)
  - 473 medium categories (groups)
  - 1,380 small categories (classes)
  - Published on June 30, 2017, implemented on October 1, 2017
  - First amendment published in March 2019

#### 1.2 International Standards Collection
- **Data Source**: UN Statistics Division
- **Main Document**: International Standard Industrial Classification Revision 5 (ISIC Rev.5)
- **Download URL**: https://unstats.un.org/unsd/classifications/Family/Detail/2095
- **Download Time**: July 8, 2025
- **Standard Features**:
  - Endorsed by UN Statistical Commission at its 54th session in March 2023
  - 22 sections (A-V)
  - 87 divisions (two-digit codes)
  - 258 groups (three-digit codes)
  - 463 classes (four-digit codes)
  - Countries expected to adapt 2025-2026, implement from 2027

### 2. Data Processing Phase

#### 2.1 Data Parsing
- Extracted complete classification structure from official websites and documents
- Identified hierarchical relationships and coding systems
- Collected names, codes, and descriptions for each classification

#### 2.2 CSV Format Conversion
Converted collected data into standardized CSV format with the following fields:
- Classification Level
- Classification Code
- Classification Name
- Parent Code
- Core Keywords
- Notes

### 3. File Creation Phase

#### 3.1 Chinese Version Classification Manual
- **Filename**: `GB_T_4754_2017_Chinese.csv`
- **Content**: Complete Chinese classification system based on GB/T 4754-2017
- **Features**: 
  - Complete hierarchical structure of 20 major categories
  - Core keywords provided for each classification
  - Suitable for Chinese domestic enterprises and government departments

#### 3.2 English Version Classification Manual
- **Filename**: `GB_T_4754_2017_English.csv`
- **Content**: English translation of GB/T 4754-2017
- **Features**:
  - Facilitates international communication and comparison
  - Supports multinational enterprise usage
  - Maintains consistent structure with Chinese version

#### 3.3 ISIC Rev.5 English Version
- **Filename**: `ISIC_Rev5_English.csv`
- **Content**: International Standard Industrial Classification Revision 5
- **Features**:
  - 22 sections of international standard classification
  - Suitable for international comparison and statistics
  - Includes latest industry development trends

#### 3.4 ISIC Rev.5 Chinese Version
- **Filename**: `ISIC_Rev5_Chinese.csv`
- **Content**: Chinese translation of ISIC Rev.5
- **Features**:
  - Facilitates understanding and usage by Chinese users
  - Supports localization of international standards

### 4. Key Feature Design

#### 4.1 Hierarchical Structure
- **Chinese Standard**: Section → Division → Group → Class
- **International Standard**: Section → Division → Group → Class

#### 4.2 Keyword Matching System
Core keywords defined for each classification to support:
- Automated industry identification
- Enterprise classification matching
- Search and filtering functions
- Machine learning training data

#### 4.3 Example Keywords
- **Manufacturing**: manufacturing processing;industrial production;processing industry;manufacturing enterprises
- **Information Technology**: software development;information technology;IT services;system integration;software services
- **Financial Services**: finance;banking;insurance;securities;investment;financial services

### 5. Technical Implementation

#### 5.1 Data Format
- CSV format adopted for easy data exchange and processing
- UTF-8 encoding supporting Chinese and English characters
- Standardized field names for programmatic processing

#### 5.2 File Organization
```
Classification/
├── GB_T_4754_2017_Chinese.csv    # Chinese standard Chinese version
├── GB_T_4754_2017_English.csv    # Chinese standard English version
├── ISIC_Rev5_English.csv          # International standard English version
├── ISIC_Rev5_Chinese.csv          # International standard Chinese version
├── Classification_Workflow.md     # Workflow documentation (Chinese)
└── Classification_Workflow_EN.md  # Workflow documentation (English)
```

### 6. Application Scenarios

#### 6.1 Enterprise Classification
- Industry classification for new enterprise registration
- Reclassification when enterprises change business scope
- Industry attribution in statistical reports

#### 6.2 Government Management
- Industry policy formulation and implementation
- Economic statistics and analysis
- Regulatory and licensing management

#### 6.3 Academic Research
- Industry analysis and comparative studies
- Economic structure research
- International comparative studies

#### 6.4 Business Applications
- Market segmentation and positioning
- Competitive analysis
- Investment decision support

### 7. Maintenance and Updates

#### 7.1 Regular Updates
- Track standard revisions and updates
- Adjust keywords based on industry developments
- Maintain consistency with latest standards

#### 7.2 Quality Control
- Regularly verify classification accuracy
- Collect user feedback and improve
- Ensure translation quality and consistency

### 8. Technical Specifications

#### 8.1 Data Validation
- Code uniqueness verification
- Hierarchical relationship consistency validation
- Keyword duplication checking

#### 8.2 Compatibility
- Support for mainstream data processing tools
- Compatible with different operating systems
- Adaptable to various programming languages

## Project Summary

This project successfully created an industry classification coding rules system based on Chinese government standards and international standards, providing complete Chinese-English bilingual versions with core matching keywords defined for each classification. The system can be widely applied in enterprise classification, government management, academic research, and business applications, providing standardized solutions for industry classification and identification.

Through standardized CSV format and detailed keyword systems, this classification system has good scalability and practicality, meeting the needs of different users and supporting automated processing and machine learning applications.

## Key Achievements

### Created Files:
1. **GB_T_4754_2017_Chinese.csv** - Chinese National Economic Industry Classification Standard (Chinese version)
2. **GB_T_4754_2017_English.csv** - Chinese National Economic Industry Classification Standard (English version)
3. **ISIC_Rev5_Chinese.csv** - International Standard Industrial Classification Rev.5 (Chinese version)
4. **ISIC_Rev5_English.csv** - International Standard Industrial Classification Rev.5 (English version)
5. **Classification_Workflow.md** - Complete workflow documentation (Chinese)
6. **Classification_Workflow_EN.md** - Complete workflow documentation (English)

### Main Features:
- **Dual Standards**: Based on both Chinese GB/T 4754-2017 and international ISIC Rev.5 standards
- **Bilingual Support**: Complete Chinese-English bilingual versions provided
- **Hierarchical Structure**: Complete classification hierarchy system included
- **Keyword Matching**: Core keywords defined for each classification supporting automated identification
- **Standardized Format**: CSV format adopted for easy data processing and application

### Download Source Records:
- **Chinese Standard**: National Bureau of Statistics website (stats.gov.cn)
- **International Standard**: UN Statistics Division (unstats.un.org)
- **Download Time**: July 8, 2025

All files are saved in the `Classification/` folder and can be directly used for enterprise classification, government management, academic research, and business applications.

---

*Document Creation Time: July 8, 2025*  
*Last Updated: July 8, 2025*  
*Version: 1.0*