#!/usr/bin/env python3
import csv
import json
import re
from io import StringIO

def clean_text(text):
    """Clean up text by removing _x000D_ and other artifacts"""
    if not text:
        return ""
    
    # Replace _x000D_ with actual line breaks
    text = text.replace('_x000D_', '\n')
    
    # Clean up extra whitespace while preserving structure
    lines = text.split('\n')
    cleaned_lines = []
    for line in lines:
        line = line.strip()
        if line:
            cleaned_lines.append(line)
    
    # Join lines back with single spaces for compact format
    result = ' '.join(cleaned_lines)
    
    # Clean up multiple spaces
    result = re.sub(r'\s+', ' ', result)
    
    return result.strip()

def escape_js_string(text):
    """Escape text for JavaScript string literals"""
    if not text:
        return ""
    
    # Replace problematic characters in order
    text = text.replace('\\', '\\\\')  # Escape backslashes first
    text = text.replace('"', '\\"')   # Escape double quotes
    text = text.replace('\n', '\\n')  # Escape newlines
    text = text.replace('\r', '\\r')  # Escape carriage returns
    text = text.replace('\t', '\\t')  # Escape tabs
    
    return text

def process_csv_to_js():
    input_file = '/mnt/d/Research/start-up/Classification/final_data/ISIC_Rev5/ISIC_Rev5_en.csv'
    output_file = '/mnt/d/Research/start-up/Classification/isic_rev5_data_compact.js'
    
    data = []
    
    # Read the entire file and process it properly
    with open(input_file, 'r', encoding='utf-8') as file:
        content = file.read()
    
    # Split into lines but keep track of multi-line CSV entries
    lines = content.split('\n')
    header = lines[0].strip()
    
    current_row = []
    in_quoted_field = False
    quote_char = None
    
    for line_idx in range(1, len(lines)):
        line = lines[line_idx]
        
        if not line.strip():
            continue
            
        # Simple state machine to handle CSV with embedded newlines
        i = 0
        while i < len(line):
            char = line[i]
            
            if not in_quoted_field:
                if char in ['"', "'"]:
                    in_quoted_field = True
                    quote_char = char
                    if not current_row:
                        current_row = ['']
                    current_row[-1] += char
                elif char == ',':
                    if not current_row:
                        current_row = ['']
                    current_row.append('')
                else:
                    if not current_row:
                        current_row = ['']
                    current_row[-1] += char
            else:
                if char == quote_char:
                    # Check if this is an escaped quote
                    if i + 1 < len(line) and line[i + 1] == quote_char:
                        current_row[-1] += char + char
                        i += 1  # Skip next quote
                    else:
                        in_quoted_field = False
                        quote_char = None
                        current_row[-1] += char
                else:
                    current_row[-1] += char
            
            i += 1
        
        # Check if we're at the end of a record
        if not in_quoted_field and len(current_row) >= 3:
            # Check if first field is a classification level
            first_field = current_row[0].strip().strip('"\'')
            if first_field in ['Section', 'Division', 'Group', 'Class']:
                # Process this record
                record = {
                    'level': clean_text(current_row[0].strip().strip('"\'')),
                    'code': clean_text(current_row[1].strip().strip('"\'')),
                    'name': clean_text(current_row[2].strip().strip('"\'')),
                    'parentCode': clean_text(current_row[3].strip().strip('"\''')) if len(current_row) > 3 else '',
                    'keywords': clean_text(current_row[4].strip().strip('"\''')) if len(current_row) > 4 else '',
                    'note': clean_text(current_row[5].strip().strip('"\''')) if len(current_row) > 5 else ''
                }
                data.append(record)
                current_row = []
        
        # If we're still in a quoted field, add a space to continue on next line
        if in_quoted_field:
            if current_row:
                current_row[-1] += ' '
    
    print(f"Processed {len(data)} records")
    
    # Generate JavaScript code
    js_content = "const isicData = [\n"
    
    for i, record in enumerate(data):
        js_content += "  {\n"
        js_content += f'    level: "{escape_js_string(record["level"])}",\n'
        js_content += f'    code: "{escape_js_string(record["code"])}",\n'
        js_content += f'    name: "{escape_js_string(record["name"])}",\n'
        js_content += f'    parentCode: "{escape_js_string(record["parentCode"])}",\n'
        js_content += f'    keywords: "{escape_js_string(record["keywords"])}",\n'
        js_content += f'    note: "{escape_js_string(record["note"])}"\n'
        js_content += "  }"
        
        if i < len(data) - 1:
            js_content += ","
        js_content += "\n"
    
    js_content += "];\n"
    
    # Add export for module use
    js_content += "\n// For use in Node.js or ES6 modules\n"
    js_content += "if (typeof module !== 'undefined' && module.exports) {\n"
    js_content += "  module.exports = isicData;\n"
    js_content += "}\n"
    
    # Write to file
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(js_content)
    
    print(f"Output written to: {output_file}")
    return output_file

if __name__ == "__main__":
    output_path = process_csv_to_js()
    print(f"Conversion complete: {output_path}")