#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import re

def final_fix():
    """
    最终修复翻译问题
    """
    print("开始最终修复...")
    
    # 读取文件
    df = pd.read_csv("GB_T_4754_2017_converted_english_final_complete.csv", encoding='utf-8')
    print(f"读取到 {len(df)} 行数据")
    
    # 修复第一列的问题
    df['Classification Level'] = df['Classification Level'].apply(fix_classification_level)
    
    # 修复其他列的剩余中文
    df['Classification Name'] = df['Classification Name'].apply(final_clean)
    df['Core Keywords'] = df['Core Keywords'].apply(final_clean) 
    df['Description'] = df['Description'].apply(final_clean)
    
    # 保存最终文件
    output_file = "GB_T_4754_2017_converted_english_FINAL.csv"
    df.to_csv(output_file, index=False, encoding='utf-8')
    print(f"最终修复完成！已保存到 {output_file}")
    print(f"共处理 {len(df)} 行数据")
    
    return output_file

def fix_classification_level(text):
    """
    修复分类级别列
    """
    if pd.isna(text):
        return text
        
    text_str = str(text).strip()
    
    # 简单映射
    if text_str == 'major categories':
        return 'Major Category'
    elif text_str == 'medium categories':
        return 'Medium Category'
    elif text_str == 'minor categories':
        return 'Minor Category'
    elif text_str == 'Sector':
        return 'Sector'
    else:
        return text_str

def final_clean(text):
    """
    最终清理函数
    """
    if pd.isna(text) or text == 'nan':
        return ''
    
    result = str(text)
    
    # 最后的中文清理
    final_replacements = {
        '民政': 'civil affairs',
        '宗教': 'religious',
        'department': 'department',
        'register': 'registration of',
        'group': 'organizations',
        'activities': 'activities',
        '在': 'in',
        'government': 'government',
        'affairs': 'affairs',
        'venue': 'venues',
        '指': 'refers to',
        'cities': 'urban',
        '镇': 'town',
        '居民': 'residents',
        '通': 'through',
        '选举': 'elections',
        'occur': 'produced',
        '群众性': 'mass',
        '自治': 'autonomous',
        'organization': 'organizations',
        'manage': 'management',
        'rural areas': 'rural',
        '村民': 'villagers',
        'unite': 'United',
        '国': 'Nations',
        '驻': 'stationed in',
        'China': 'China',
        'within China': 'within China',
        'institution': 'institutions',
        'etc.': 'and other',
        
        # 数字修复
        'includes01-05': 'includes 01-05',
        'includes06-12': 'includes 06-12', 
        'includes13-43': 'includes 13-43',
        'includes97': 'includes 97',
        
        # 空格问题修复
        '  ': ' ',
        '   ': ' ',
        ' ,': ',',
        ' .': '.',
        ' ;': ';',
        ' :': ':',
    }
    
    for old, new in final_replacements.items():
        result = result.replace(old, new)
    
    # 清理多余空格
    result = re.sub(r'\s+', ' ', result)
    
    return result.strip()

if __name__ == "__main__":
    final_fix()