#!/usr/bin/env python3
# -*- coding: utf-8 -*-

def create_complete_gb_classification():
    """Create complete GB/T 4754-2017 classification including manually verified sections"""
    
    classifications = []
    
    # 门类 A - 农、林、牧、渔业 (manually extracted)
    classifications.extend([
        ('门类', 'A', '农、林、牧、渔业', ''),
        ('大类', '01', '农业', 'A'),
        ('大类', '02', '林业', 'A'),
        ('大类', '03', '畜牧业', 'A'),
        ('大类', '04', '渔业', 'A'),
        ('大类', '05', '农、林、牧、渔专业及辅助性活动', 'A'),
        
        # 01 农业的中类和小类
        ('中类', '011', '谷物种植', '01'),
        ('小类', '0111', '稻谷种植', '011'),
        ('小类', '0112', '小麦种植', '011'),
        ('小类', '0113', '玉米种植', '011'),
        ('小类', '0119', '其他谷物种植', '011'),
        
        ('中类', '012', '豆类、油料和薯类种植', '01'),
        ('小类', '0121', '豆类种植', '012'),
        ('小类', '0122', '油料种植', '012'),
        ('小类', '0123', '薯类种植', '012'),
        
        ('中类', '013', '棉、麻、糖、烟草种植', '01'),
        ('小类', '0131', '棉花种植', '013'),
        ('小类', '0132', '麻类种植', '013'),
        ('小类', '0133', '糖料种植', '013'),
        ('小类', '0134', '烟草种植', '013'),
        
        ('中类', '014', '蔬菜、食用菌及园艺作物种植', '01'),
        ('小类', '0141', '蔬菜种植', '014'),
        ('小类', '0142', '食用菌种植', '014'),
        ('小类', '0143', '花卉种植', '014'),
        ('小类', '0149', '其他园艺作物种植', '014'),
        
        ('中类', '015', '水果种植', '01'),
        ('小类', '0151', '仁果类和核果类水果种植', '015'),
        ('小类', '0152', '葡萄种植', '015'),
        ('小类', '0153', '柑橘类种植', '015'),
        ('小类', '0154', '香蕉等亚热带水果种植', '015'),
        ('小类', '0159', '其他水果种植', '015'),
        
        ('中类', '016', '坚果、含油果、香料和饮料作物种植', '01'),
        ('小类', '0161', '坚果种植', '016'),
        ('小类', '0162', '含油果种植', '016'),
        ('小类', '0163', '香料作物种植', '016'),
        ('小类', '0164', '茶叶种植', '016'),
        ('小类', '0169', '其他饮料作物种植', '016'),
        
        ('中类', '017', '中药材种植', '01'),
        ('小类', '0171', '中草药种植', '017'),
        ('小类', '0179', '其他中药材种植', '017'),
        
        ('中类', '018', '草种植及割草', '01'),
        ('小类', '0181', '草种植', '018'),
        ('小类', '0182', '天然草原割草', '018'),
        
        ('中类', '019', '其他农业', '01'),
        ('小类', '0190', '其他农业', '019'),
    ])
    
    # 门类 B - 采矿业
    classifications.extend([
        ('门类', 'B', '采矿业', ''),
        ('大类', '06', '煤炭开采和洗选业', 'B'),
        ('大类', '07', '石油和天然气开采业', 'B'),
        ('大类', '08', '黑色金属矿采选业', 'B'),
        ('大类', '09', '有色金属矿采选业', 'B'),
        ('大类', '10', '非金属矿采选业', 'B'),
        ('大类', '11', '开采专业及辅助性活动', 'B'),
        ('大类', '12', '其他采矿业', 'B'),
    ])
    
    # 门类 C - 制造业
    classifications.extend([
        ('门类', 'C', '制造业', ''),
        ('大类', '13', '农副食品加工业', 'C'),
        ('大类', '14', '食品制造业', 'C'),
        ('大类', '15', '酒、饮料和精制茶制造业', 'C'),
        ('大类', '16', '烟草制品业', 'C'),
        ('大类', '17', '纺织业', 'C'),
        ('大类', '18', '纺织服装、服饰业', 'C'),
        ('大类', '19', '皮革、毛皮、羽毛及其制品和制鞋业', 'C'),
        ('大类', '20', '木材加工和木、竹、藤、棕、草制品业', 'C'),
        ('大类', '21', '家具制造业', 'C'),
        ('大类', '22', '造纸和纸制品业', 'C'),
        ('大类', '23', '印刷和记录媒介复制业', 'C'),
        ('大类', '24', '文教、工美、体育和娱乐用品制造业', 'C'),
        ('大类', '25', '石油加工、炼焦和核燃料加工业', 'C'),
        ('大类', '26', '化学原料和化学制品制造业', 'C'),
        ('大类', '27', '医药制造业', 'C'),
        ('大类', '28', '化学纤维制造业', 'C'),
        ('大类', '29', '橡胶和塑料制品业', 'C'),
        ('大类', '30', '非金属矿物制品业', 'C'),
        ('大类', '31', '黑色金属冶炼和压延加工业', 'C'),
        ('大类', '32', '有色金属冶炼和压延加工业', 'C'),
        ('大类', '33', '金属制品业', 'C'),
        ('大类', '34', '通用设备制造业', 'C'),
        ('大类', '35', '专用设备制造业', 'C'),
        ('大类', '36', '汽车制造业', 'C'),
        ('大类', '37', '铁路、船舶、航空航天和其他运输设备制造业', 'C'),
        ('大类', '38', '电气机械和器材制造业', 'C'),
        ('大类', '39', '计算机、通信和其他电子设备制造业', 'C'),
        ('大类', '40', '仪器仪表制造业', 'C'),
        ('大类', '41', '其他制造业', 'C'),
        ('大类', '42', '废弃资源综合利用业', 'C'),
        ('大类', '43', '金属制品、机械和设备修理业', 'C'),
    ])
    
    # 门类 D - 电力、热力、燃气及水生产和供应业
    classifications.extend([
        ('门类', 'D', '电力、热力、燃气及水生产和供应业', ''),
        ('大类', '44', '电力、热力生产和供应业', 'D'),
        ('大类', '45', '燃气生产和供应业', 'D'),
        ('大类', '46', '水的生产和供应业', 'D'),
    ])
    
    # 门类 E - 建筑业
    classifications.extend([
        ('门类', 'E', '建筑业', ''),
        ('大类', '47', '房屋建筑业', 'E'),
        ('大类', '48', '土木工程建筑业', 'E'),
        ('大类', '49', '建筑安装业', 'E'),
        ('大类', '50', '建筑装饰、装修和其他建筑业', 'E'),
    ])
    
    # 门类 F - 批发和零售业
    classifications.extend([
        ('门类', 'F', '批发和零售业', ''),
        ('大类', '51', '批发业', 'F'),
        ('大类', '52', '零售业', 'F'),
    ])
    
    # 门类 G - 交通运输、仓储和邮政业
    classifications.extend([
        ('门类', 'G', '交通运输、仓储和邮政业', ''),
        ('大类', '53', '铁路运输业', 'G'),
        ('大类', '54', '道路运输业', 'G'),
        ('大类', '55', '水上运输业', 'G'),
        ('大类', '56', '航空运输业', 'G'),
        ('大类', '57', '管道运输业', 'G'),
        ('大类', '58', '多式联运和运输代理业', 'G'),
        ('大类', '59', '仓储业', 'G'),
        ('大类', '60', '邮政业', 'G'),
    ])
    
    # 门类 H - 住宿和餐饮业
    classifications.extend([
        ('门类', 'H', '住宿和餐饮业', ''),
        ('大类', '61', '住宿业', 'H'),
        ('大类', '62', '餐饮业', 'H'),
    ])
    
    # 门类 I - 信息传输、软件和信息技术服务业
    classifications.extend([
        ('门类', 'I', '信息传输、软件和信息技术服务业', ''),
        ('大类', '63', '电信、广播电视和卫星传输服务', 'I'),
        ('大类', '64', '互联网和相关服务', 'I'),
        ('大类', '65', '软件和信息技术服务业', 'I'),
    ])
    
    # 门类 J - 金融业
    classifications.extend([
        ('门类', 'J', '金融业', ''),
        ('大类', '66', '货币金融服务', 'J'),
        ('大类', '67', '资本市场服务', 'J'),
        ('大类', '68', '保险业', 'J'),
        ('大类', '69', '其他金融业', 'J'),
    ])
    
    # 门类 K - 房地产业
    classifications.extend([
        ('门类', 'K', '房地产业', ''),
        ('大类', '70', '房地产业', 'K'),
    ])
    
    # 门类 L - 租赁和商务服务业
    classifications.extend([
        ('门类', 'L', '租赁和商务服务业', ''),
        ('大类', '71', '租赁业', 'L'),
        ('大类', '72', '商务服务业', 'L'),
    ])
    
    # 门类 M - 科学研究和技术服务业
    classifications.extend([
        ('门类', 'M', '科学研究和技术服务业', ''),
        ('大类', '73', '研究和试验发展', 'M'),
        ('大类', '74', '专业技术服务业', 'M'),
        ('大类', '75', '科技推广和应用服务业', 'M'),
    ])
    
    # 门类 N - 水利、环境和公共设施管理业
    classifications.extend([
        ('门类', 'N', '水利、环境和公共设施管理业', ''),
        ('大类', '76', '水利管理业', 'N'),
        ('大类', '77', '生态保护和环境治理业', 'N'),
        ('大类', '78', '公共设施管理业', 'N'),
    ])
    
    # 门类 O - 居民服务、修理和其他服务业
    classifications.extend([
        ('门类', 'O', '居民服务、修理和其他服务业', ''),
        ('大类', '79', '居民服务业', 'O'),
        ('大类', '80', '机动车、电子产品和日用产品修理业', 'O'),
        ('大类', '81', '其他服务业', 'O'),
    ])
    
    # 门类 P - 教育
    classifications.extend([
        ('门类', 'P', '教育', ''),
        ('大类', '82', '教育', 'P'),
    ])
    
    # 门类 Q - 卫生和社会工作
    classifications.extend([
        ('门类', 'Q', '卫生和社会工作', ''),
        ('大类', '83', '卫生', 'Q'),
        ('大类', '84', '社会工作', 'Q'),
    ])
    
    # 门类 R - 文化、体育和娱乐业
    classifications.extend([
        ('门类', 'R', '文化、体育和娱乐业', ''),
        ('大类', '85', '新闻和出版业', 'R'),
        ('大类', '86', '广播、电视、电影和录音制作业', 'R'),
        ('大类', '87', '文化艺术业', 'R'),
        ('大类', '88', '体育', 'R'),
        ('大类', '89', '娱乐业', 'R'),
    ])
    
    # 门类 S - 公共管理、社会保障和社会组织
    classifications.extend([
        ('门类', 'S', '公共管理、社会保障和社会组织', ''),
        ('大类', '90', '中国共产党机关', 'S'),
        ('大类', '91', '国家机构', 'S'),
        ('大类', '92', '人民政协、民主党派', 'S'),
        ('大类', '93', '社会保障', 'S'),
        ('大类', '94', '群众团体、社会团体和其他成员组织', 'S'),
        ('大类', '95', '基层群众自治组织', 'S'),
    ])
    
    # 门类 T - 国际组织
    classifications.extend([
        ('门类', 'T', '国际组织', ''),
        ('大类', '96', '国际组织', 'T'),
    ])
    
    return classifications

def load_extracted_data():
    """Load additional extracted data from previous run"""
    extracted_data = []
    
    try:
        with open('/mnt/d/Research/start-up/Classification/gb_t_4754_2017_complete.csv', 'r', encoding='utf-8') as f:
            lines = f.readlines()[1:]  # Skip header
            for line in lines:
                parts = line.strip().split(',')
                if len(parts) >= 4:
                    level, code, name, parent = parts[:4]
                    extracted_data.append((level, code, name, parent))
    except FileNotFoundError:
        pass
    
    return extracted_data

def merge_and_deduplicate(manual_data, extracted_data):
    """Merge manual and extracted data, removing duplicates"""
    seen = set()
    result = []
    
    # Add manual data first (higher priority)
    for item in manual_data:
        key = (item[0], item[1])  # level and code
        if key not in seen:
            result.append(item)
            seen.add(key)
    
    # Add extracted data if not already present
    for item in extracted_data:
        key = (item[0], item[1])  # level and code
        if key not in seen:
            result.append(item)
            seen.add(key)
    
    return result

def format_csv_output(classifications):
    """Format classifications as CSV"""
    output = ['分类级别,分类代码,分类名称,上级代码']
    
    for level, code, name, parent in classifications:
        # Clean up names
        clean_name = name.replace(',', '，')  # Replace comma with Chinese comma
        clean_name = clean_name.replace('"', '"')   # Replace quotes
        output.append(f'{level},{code},{clean_name},{parent}')
    
    return '\n'.join(output)

if __name__ == "__main__":
    print("生成完整的GB/T 4754-2017行业分类数据...")
    
    # Get manually curated complete structure
    manual_data = create_complete_gb_classification()
    
    # Load any previously extracted data
    extracted_data = load_extracted_data()
    
    # Merge and deduplicate
    all_classifications = merge_and_deduplicate(manual_data, extracted_data)
    
    # Format as CSV
    csv_output = format_csv_output(all_classifications)
    
    # Write to file
    output_file = '/mnt/d/Research/start-up/Classification/gb_t_4754_2017_final.csv'
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(csv_output)
    
    print(f"完整分类数据已保存到: {output_file}")
    
    # Show statistics
    stats = {}
    for level, code, name, parent in all_classifications:
        stats[level] = stats.get(level, 0) + 1
    
    print("\n分类统计:")
    for level in ['门类', '大类', '中类', '小类']:
        if level in stats:
            print(f"{level}: {stats[level]} 个")
    
    print(f"\n总记录数: {len(all_classifications)}")
    
    # Show first 50 entries
    print("\n前50条记录预览:")
    lines = csv_output.split('\n')
    for line in lines[:51]:
        print(line)