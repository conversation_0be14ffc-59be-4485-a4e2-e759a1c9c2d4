#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re
import html

def extract_table_data(html_file_path):
    """
    从HTML文件中提取国民经济行业分类表格数据
    """
    print(f"正在读取文件: {html_file_path}")
    
    try:
        with open(html_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
    except Exception as e:
        print(f"读取文件失败: {e}")
        return
    
    print(f"文件大小: {len(content)} 字符")
    
    # 搜索表格内容
    # 首先查找表格标签
    table_pattern = r'<table[^>]*>(.*?)</table>'
    tables = re.findall(table_pattern, content, re.DOTALL | re.IGNORECASE)
    
    print(f"找到 {len(tables)} 个表格")
    
    if not tables:
        # 如果没有标准表格，搜索可能的数据模式
        print("未找到标准表格标签，搜索可能的数据模式...")
        
        # 搜索代码和行业名称的模式
        # A01, B01, C01 等代码模式
        code_pattern = r'([A-Z]\d{2,3})\s*([^\n<>]+?)(?=\s*[A-Z]\d{2,3}|\s*$|<|>)'
        matches = re.findall(code_pattern, content)
        
        if matches:
            print(f"找到 {len(matches)} 个代码-名称匹配")
            for i, (code, name) in enumerate(matches[:20]):  # 显示前20个
                print(f"{i+1:3d}. {code} - {name.strip()}")
        
        # 搜索门类、大类、中类、小类等关键词附近的内容
        category_pattern = r'(门类|大类|中类|小类)[：:]?\s*([A-Z]?\d*)\s*([^<>\n]{1,100})'
        category_matches = re.findall(category_pattern, content)
        
        if category_matches:
            print(f"\n找到 {len(category_matches)} 个分类匹配:")
            for i, (level, code, name) in enumerate(category_matches[:10]):
                print(f"{i+1:3d}. {level}: {code} {name.strip()}")
    
    else:
        # 解析表格内容
        for i, table_content in enumerate(tables):
            print(f"\n=== 表格 {i+1} ===")
            
            # 提取表格行
            row_pattern = r'<tr[^>]*>(.*?)</tr>'
            rows = re.findall(row_pattern, table_content, re.DOTALL | re.IGNORECASE)
            
            print(f"表格中有 {len(rows)} 行")
            
            for j, row in enumerate(rows[:10]):  # 显示前10行
                # 提取单元格
                cell_pattern = r'<t[dh][^>]*>(.*?)</t[dh]>'
                cells = re.findall(cell_pattern, row, re.DOTALL | re.IGNORECASE)
                
                # 清理HTML标签和解码HTML实体
                clean_cells = []
                for cell in cells:
                    # 移除HTML标签
                    clean_cell = re.sub(r'<[^>]+>', '', cell)
                    # 解码HTML实体
                    clean_cell = html.unescape(clean_cell)
                    # 清理空白字符
                    clean_cell = ' '.join(clean_cell.split())
                    clean_cells.append(clean_cell)
                
                if clean_cells:
                    print(f"行 {j+1:3d}: {' | '.join(clean_cells)}")
    
    # 搜索具体的行业代码模式
    print("\n=== 搜索具体行业代码 ===")
    
    # 搜索 A01, A02 等模式
    specific_pattern = r'([A-Z]\d{2})\s+([^<>\n\r]{10,100})'
    specific_matches = re.findall(specific_pattern, content)
    
    print(f"找到 {len(specific_matches)} 个具体代码匹配:")
    for i, (code, name) in enumerate(specific_matches[:30]):
        print(f"{code} - {name.strip()}")
    
    # 搜索数字代码模式（如 01, 011, 0111等）
    print("\n=== 搜索数字代码模式 ===")
    digit_pattern = r'(\d{2,4})\s+([^<>\n\r\d]{10,100})'
    digit_matches = re.findall(digit_pattern, content)
    
    print(f"找到 {len(digit_matches)} 个数字代码匹配:")
    for i, (code, name) in enumerate(digit_matches[:30]):
        print(f"{code} - {name.strip()}")

if __name__ == "__main__":
    html_file = "/mnt/d/Research/start-up/Classification/raw/国民经济行业分类-国民经济行业代码表（GB_T 4754-2017) (2025_7_9 02：24：18).html"
    extract_table_data(html_file)