#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re
import html

def extract_gb_classification_regex():
    """Extract GB/T 4754-2017 classification using regex patterns"""
    
    print("开始使用正则表达式解析HTML文件...")
    
    # Read the HTML file
    with open('/mnt/d/Research/start-up/Classification/raw/国民经济行业分类-国民经济行业代码表（GB_T 4754-2017) (2025_7_9 02：24：18).html', 'r', encoding='utf-8') as f:
        content = f.read()
    
    classifications = []
    current_section = ""
    current_division = ""
    current_group = ""
    
    # Process the content line by line looking for table patterns
    print("解析表格数据...")
    
    # 门类 patterns - looking for single letters A-T followed by category names
    section_pattern = r'<td><b>([A-T])</b><td><b>&nbsp;</b><td> &nbsp;<td> &nbsp;<td><b>([^<]+)</b><td>'
    sections = re.findall(section_pattern, content)
    
    for code, name in sections:
        name = html.unescape(name).strip()
        if name and name not in ['门类', '大类', '中类', '小类']:
            classifications.append(('门类', code, name, ''))
            current_section = code
            print(f"门类: {code} - {name}")
    
    # 大类 patterns - looking for two-digit codes
    division_pattern = r'<td>&nbsp;<td><b>([0-9]{2})</b><td><b>&nbsp;</b><td> &nbsp;<td><b>([^<]+)</b><td>'
    divisions = re.findall(division_pattern, content)
    
    # Map divisions to their sections based on the order in content
    section_ranges = {
        'A': (1, 5),   # 01-05
        'B': (6, 12),  # 06-12  
        'C': (13, 43), # 13-43
        'D': (44, 46), # 44-46
        'E': (47, 50), # 47-50
        'F': (51, 53), # 51-53
        'G': (54, 68), # 54-68
        'H': (69, 70), # 69-70
        'I': (71, 75), # 71-75
        'J': (76, 82), # 76-82
        'K': (83, 84), # 83-84
        'L': (85, 85), # 85
        'M': (86, 89), # 86-89
        'N': (90, 96), # 90-96
        'O': (97, 97), # 97
        'P': (98, 98), # 98
        'Q': (99, 99), # 99
        'R': (100, 102), # 100-102
        'S': (103, 104), # 103-104
        'T': (105, 105), # 105
    }
    
    def get_section_for_division(div_code):
        div_num = int(div_code)
        for section, (start, end) in section_ranges.items():
            if start <= div_num <= end:
                return section
        return ""
    
    for code, name in divisions:
        name = html.unescape(name).strip()
        if name and name not in ['门类', '大类', '中类', '小类']:
            parent_section = get_section_for_division(code)
            classifications.append(('大类', code, name, parent_section))
            print(f"大类: {code} - {name} (属于门类: {parent_section})")
    
    # 中类 patterns - looking for three-digit codes  
    group_pattern = r'<td> &nbsp;<td> &nbsp;<td> ([0-9]{3})<td> &nbsp;<td> &nbsp; ([^<]+)<td>'
    groups = re.findall(group_pattern, content)
    
    for code, name in groups:
        name = html.unescape(name).strip()
        if name and name not in ['门类', '大类', '中类', '小类'] and not re.match(r'^[0-9]+$', name):
            parent_division = code[:2]  # First two digits
            classifications.append(('中类', code, name, parent_division))
            print(f"中类: {code} - {name} (属于大类: {parent_division})")
    
    # 小类 patterns - looking for four-digit codes
    class_pattern = r'<td> &nbsp;<td> &nbsp;<td> &nbsp;<td> ([0-9]{4})<td> ([^<]+)<td>'
    classes = re.findall(class_pattern, content)
    
    for code, name in classes:
        name = html.unescape(name).strip()
        if name and name not in ['门类', '大类', '中类', '小类'] and not re.match(r'^[0-9]+$', name):
            parent_group = code[:3]  # First three digits
            classifications.append(('小类', code, name, parent_group))
            print(f"小类: {code} - {name} (属于中类: {parent_group})")
    
    print(f"\n提取完成，共获得 {len(classifications)} 条分类记录")
    return classifications

def format_csv_output(classifications):
    """Format classifications as CSV"""
    output = ['分类级别,分类代码,分类名称,上级代码']
    
    for level, code, name, parent in classifications:
        # Clean up names
        clean_name = re.sub(r'\s+', ' ', name).strip()
        clean_name = clean_name.replace(',', '，')  # Replace comma with Chinese comma
        clean_name = clean_name.replace('"', '"')   # Replace quotes
        # Remove common description indicators
        clean_name = re.sub(r'指.*$', '', clean_name).strip()
        clean_name = re.sub(r'包括.*$', '', clean_name).strip()
        output.append(f'{level},{code},{clean_name},{parent}')
    
    return '\n'.join(output)

if __name__ == "__main__":
    try:
        classifications = extract_gb_classification_regex()
        
        if classifications:
            csv_output = format_csv_output(classifications)
            
            # Write to file
            output_file = '/mnt/d/Research/start-up/Classification/gb_t_4754_2017_complete.csv'
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(csv_output)
            
            print(f"\n完整分类数据已保存到: {output_file}")
            
            # Show statistics
            stats = {}
            for level, code, name, parent in classifications:
                stats[level] = stats.get(level, 0) + 1
            
            print("\n分类统计:")
            for level in ['门类', '大类', '中类', '小类']:
                if level in stats:
                    print(f"{level}: {stats[level]} 个")
            
            # Show first 20 entries
            print("\n前20条记录预览:")
            lines = csv_output.split('\n')
            for line in lines[:21]:
                print(line)
            
        else:
            print("未能提取到分类数据，请检查HTML文件格式")
            
    except Exception as e:
        print(f"处理过程中出现错误: {e}")
        import traceback
        traceback.print_exc()