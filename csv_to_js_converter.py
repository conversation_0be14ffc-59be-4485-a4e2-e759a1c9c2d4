#!/usr/bin/env python3
import csv
import json
import re

def clean_text(text):
    """Clean up text by removing _x000D_ and other artifacts, properly handling quotes"""
    if not text:
        return ""
    
    # Replace _x000D_ with actual line breaks
    text = text.replace('_x000D_', '\n')
    
    # Clean up extra whitespace while preserving structure
    lines = text.split('\n')
    cleaned_lines = []
    for line in lines:
        line = line.strip()
        if line:
            cleaned_lines.append(line)
    
    # Join lines back with single spaces for compact format, but preserve bullet points
    result = ' '.join(cleaned_lines)
    
    # Clean up multiple spaces
    result = re.sub(r'\s+', ' ', result)
    
    return result.strip()

def escape_js_string(text):
    """Escape text for JavaScript string literals"""
    if not text:
        return ""
    
    # Replace problematic characters
    text = text.replace('\\', '\\\\')  # Escape backslashes first
    text = text.replace('"', '\\"')   # Escape double quotes
    text = text.replace('\n', '\\n')  # Escape newlines
    text = text.replace('\r', '\\r')  # Escape carriage returns
    text = text.replace('\t', '\\t')  # Escape tabs
    
    return text

def process_csv_to_js():
    input_file = '/mnt/d/Research/start-up/Classification/final_data/ISIC_Rev5/ISIC_Rev5_en.csv'
    output_file = '/mnt/d/Research/start-up/Classification/isic_rev5_data_compact.js'
    
    data = []
    current_record = None
    
    with open(input_file, 'r', encoding='utf-8') as file:
        # Read the header
        header = file.readline().strip()
        
        for line_num, line in enumerate(file, 2):
            line = line.strip()
            if not line:
                continue
                
            # Try to parse as CSV
            try:
                reader = csv.reader([line])
                row = next(reader)
                
                # Check if this is a new record (starts with a classification level)
                if len(row) >= 3 and row[0] in ['Section', 'Division', 'Group', 'Class']:
                    # Save previous record if exists
                    if current_record:
                        data.append(current_record)
                    
                    # Start new record
                    current_record = {
                        'level': row[0],
                        'code': row[1] if len(row) > 1 else '',
                        'name': clean_text(row[2]) if len(row) > 2 else '',
                        'parentCode': row[3] if len(row) > 3 else '',
                        'keywords': clean_text(row[4]) if len(row) > 4 else '',
                        'note': clean_text(row[5]) if len(row) > 5 else ''
                    }
                else:
                    # This is a continuation line, append to note
                    if current_record and line:
                        if current_record['note']:
                            current_record['note'] += ' ' + clean_text(line)
                        else:
                            current_record['note'] = clean_text(line)
            except:
                # Handle lines that don't parse as CSV (continuation lines)
                if current_record and line:
                    if current_record['note']:
                        current_record['note'] += ' ' + clean_text(line)
                    else:
                        current_record['note'] = clean_text(line)
    
    # Don't forget the last record
    if current_record:
        data.append(current_record)
    
    # Generate JavaScript code
    js_content = "const isicData = [\n"
    
    for i, record in enumerate(data):
        js_content += "  {\n"
        js_content += f'    level: "{escape_js_string(record["level"])}",\n'
        js_content += f'    code: "{escape_js_string(record["code"])}",\n'
        js_content += f'    name: "{escape_js_string(record["name"])}",\n'
        js_content += f'    parentCode: "{escape_js_string(record["parentCode"])}",\n'
        js_content += f'    keywords: "{escape_js_string(record["keywords"])}",\n'
        js_content += f'    note: "{escape_js_string(record["note"])}"\n'
        js_content += "  }"
        
        if i < len(data) - 1:
            js_content += ","
        js_content += "\n"
    
    js_content += "];\n"
    
    # Add export for module use
    js_content += "\n// For use in Node.js or ES6 modules\n"
    js_content += "if (typeof module !== 'undefined' && module.exports) {\n"
    js_content += "  module.exports = isicData;\n"
    js_content += "}\n"
    
    # Write to file
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(js_content)
    
    print(f"Processed {len(data)} records")
    print(f"Output written to: {output_file}")
    
    return output_file

if __name__ == "__main__":
    output_path = process_csv_to_js()
    print(f"Conversion complete: {output_path}")