#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re
import html

def extract_gb_classification():
    """Extract GB/T 4754-2017 classification from HTML file"""
    
    # Read the HTML file
    with open('/mnt/d/Research/start-up/Classification/raw/国民经济行业分类-国民经济行业代码表（GB_T 4754-2017) (2025_7_9 02：24：18).html', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Split content by table rows
    rows = content.split('<tr>')
    
    classifications = []
    current_section = ""
    current_division = ""
    current_group = ""
    
    # Process each row
    for row in rows:
        # Clean up HTML tags and decode entities
        clean_row = re.sub(r'<[^>]+>', ' ', row)
        clean_row = html.unescape(clean_row)
        clean_row = re.sub(r'\s+', ' ', clean_row).strip()
        
        # Skip empty rows
        if not clean_row:
            continue
            
        # Pattern to match table cells with classification data
        cells = re.findall(r'<td[^>]*>(.*?)</td>', row, re.DOTALL)
        if len(cells) < 5:
            continue
            
        # Clean cell contents
        cleaned_cells = []
        for cell in cells:
            cell_text = re.sub(r'<[^>]+>', '', cell)
            cell_text = html.unescape(cell_text)
            cell_text = re.sub(r'\s+', ' ', cell_text).strip()
            cleaned_cells.append(cell_text)
        
        # Skip if not enough cells
        if len(cleaned_cells) < 5:
            continue
            
        # Look for classification patterns
        col1, col2, col3, col4, col5 = cleaned_cells[:5]
        
        # 门类 (Sections) - Single letter codes
        if re.match(r'^[A-Z]$', col1) and col5:
            current_section = col1
            current_division = ""
            current_group = ""
            name = col5.split('&nbsp;')[0].strip()
            if name and name not in ['门类', '大类', '中类', '小类']:
                classifications.append(('门类', col1, name, ''))
        
        # 大类 (Divisions) - Two digit codes  
        elif re.match(r'^[0-9]{2}$', col2) and col5:
            current_division = col2
            current_group = ""
            name = col5.split('&nbsp;')[0].strip()
            if name and name not in ['门类', '大类', '中类', '小类']:
                classifications.append(('大类', col2, name, current_section))
        
        # 中类 (Groups) - Three digit codes
        elif re.match(r'^[0-9]{3}$', col3) and col5:
            current_group = col3
            name = col5.split('&nbsp;')[0].strip()
            if name and name not in ['门类', '大类', '中类', '小类']:
                classifications.append(('中类', col3, name, current_division))
        
        # 小类 (Classes) - Four digit codes
        elif re.match(r'^[0-9]{4}$', col4) and col5:
            name = col5.split('&nbsp;')[0].strip()
            if name and name not in ['门类', '大类', '中类', '小类']:
                classifications.append(('小类', col4, name, current_group))
    
    return classifications

def format_csv_output(classifications):
    """Format classifications as CSV"""
    output = ['分类级别,分类代码,分类名称,上级代码']
    
    for level, code, name, parent in classifications:
        # Clean up names - remove extra spaces and special characters
        clean_name = re.sub(r'\s+', ' ', name).strip()
        clean_name = clean_name.replace(',', '，')  # Replace comma with Chinese comma
        output.append(f'{level},{code},{clean_name},{parent}')
    
    return '\n'.join(output)

if __name__ == "__main__":
    classifications = extract_gb_classification()
    csv_output = format_csv_output(classifications)
    
    # Write to file
    with open('/mnt/d/Research/start-up/Classification/gb_t_4754_2017_complete.csv', 'w', encoding='utf-8') as f:
        f.write(csv_output)
    
    # Print first 20 lines to console
    lines = csv_output.split('\n')
    for i, line in enumerate(lines[:21]):
        print(line)
    
    print(f"\n... 输出完整分类数据到文件，共 {len(classifications)} 条记录")