#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re
import html
from bs4 import BeautifulSoup

def comprehensive_extract():
    """Comprehensive extraction of all GB/T 4754-2017 classifications"""
    
    print("开始解析HTML文件...")
    
    # Read the HTML file
    with open('/mnt/d/Research/start-up/Classification/raw/国民经济行业分类-国民经济行业代码表（GB_T 4754-2017) (2025_7_9 02：24：18).html', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Clean and parse content
    soup = BeautifulSoup(content, 'html.parser')
    
    classifications = []
    current_section = ""
    current_division = ""
    current_group = ""
    
    # Find all table rows
    rows = soup.find_all('tr')
    
    print(f"找到 {len(rows)} 个表格行，开始处理...")
    
    for i, row in enumerate(rows):
        cells = row.find_all('td')
        if len(cells) < 5:
            continue
            
        # Extract text from cells
        cell_texts = []
        for cell in cells:
            text = cell.get_text(strip=True)
            text = re.sub(r'\s+', ' ', text)
            cell_texts.append(text)
        
        if len(cell_texts) < 5:
            continue
            
        col1, col2, col3, col4, col5 = cell_texts[:5]
        
        # Clean up text
        col1 = col1.replace('\u00a0', '').strip()
        col2 = col2.replace('\u00a0', '').strip()
        col3 = col3.replace('\u00a0', '').strip()
        col4 = col4.replace('\u00a0', '').strip()
        col5 = col5.replace('\u00a0', '').strip()
        
        # Skip header rows
        if col1 in ['门类', '大类', '中类', '小类'] or col2 in ['门类', '大类', '中类', '小类']:
            continue
            
        # 门类 (Sections) - Single letter codes A-T
        if re.match(r'^[A-T]$', col1) and col5 and len(col5) > 2:
            current_section = col1
            current_division = ""
            current_group = ""
            name = col5.split('本')[0].strip()  # Remove explanation part
            if name and not re.match(r'^[0-9]+$', name):
                classifications.append(('门类', col1, name, ''))
                print(f"门类: {col1} - {name}")
        
        # 大类 (Divisions) - Two digit codes
        elif re.match(r'^[0-9]{2}$', col2) and col5 and len(col5) > 2:
            current_division = col2
            current_group = ""
            name = col5.split('指')[0].strip()  # Remove explanation part
            if name and not re.match(r'^[0-9]+$', name):
                classifications.append(('大类', col2, name, current_section))
                print(f"大类: {col2} - {name}")
        
        # 中类 (Groups) - Three digit codes
        elif re.match(r'^[0-9]{3}$', col3) and col5 and len(col5) > 2:
            current_group = col3
            name = col5.split('指')[0].strip()  # Remove explanation part
            if name and not re.match(r'^[0-9]+$', name):
                classifications.append(('中类', col3, name, current_division))
                print(f"中类: {col3} - {name}")
        
        # 小类 (Classes) - Four digit codes
        elif re.match(r'^[0-9]{4}$', col4) and col5 and len(col5) > 1:
            name = col5.split('指')[0].strip()  # Remove explanation part
            if name and not re.match(r'^[0-9]+$', name):
                classifications.append(('小类', col4, name, current_group))
                print(f"小类: {col4} - {name}")
    
    print(f"\n提取完成，共获得 {len(classifications)} 条分类记录")
    return classifications

def format_csv_output(classifications):
    """Format classifications as CSV"""
    output = ['分类级别,分类代码,分类名称,上级代码']
    
    for level, code, name, parent in classifications:
        # Clean up names
        clean_name = re.sub(r'\s+', ' ', name).strip()
        clean_name = clean_name.replace(',', '，')  # Replace comma with Chinese comma
        clean_name = clean_name.replace('"', '"')   # Replace quotes
        output.append(f'{level},{code},{clean_name},{parent}')
    
    return '\n'.join(output)

if __name__ == "__main__":
    try:
        classifications = comprehensive_extract()
        
        if classifications:
            csv_output = format_csv_output(classifications)
            
            # Write to file
            output_file = '/mnt/d/Research/start-up/Classification/gb_t_4754_2017_complete.csv'
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(csv_output)
            
            print(f"\n完整分类数据已保存到: {output_file}")
            
            # Show statistics
            stats = {}
            for level, code, name, parent in classifications:
                stats[level] = stats.get(level, 0) + 1
            
            print("\n分类统计:")
            for level in ['门类', '大类', '中类', '小类']:
                if level in stats:
                    print(f"{level}: {stats[level]} 个")
            
            # Show first 20 entries
            print("\n前20条记录预览:")
            lines = csv_output.split('\n')
            for line in lines[:21]:
                print(line)
            
        else:
            print("未能提取到分类数据，请检查HTML文件格式")
            
    except Exception as e:
        print(f"处理过程中出现错误: {e}")
        import traceback
        traceback.print_exc()