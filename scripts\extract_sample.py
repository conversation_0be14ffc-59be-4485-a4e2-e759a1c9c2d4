#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re
import html

def extract_classification_from_text():
    """Extract GB/T 4754-2017 classification using pattern matching"""
    
    # Read and extract raw content from grep output
    import subprocess
    
    # First let's get the raw table data
    cmd = ["grep", "-o", r"<td[^>]*>.*</td>", "/mnt/d/Research/start-up/Classification/raw/国民经济行业分类-国民经济行业代码表（GB_T 4754-2017) (2025_7_9 02：24：18).html"]
    result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
    lines = result.stdout.strip().split('\n')
    
    classifications = []
    current_section = ""
    current_division = ""
    current_group = ""
    
    # Process lines to extract table structure
    i = 0
    while i < len(lines):
        line = lines[i].strip()
        if not line:
            i += 1
            continue
            
        # Clean HTML
        clean = re.sub(r'<[^>]+>', '', line)
        clean = html.unescape(clean)
        clean = re.sub(r'&nbsp;', ' ', clean)
        clean = clean.strip()
        
        # Look for specific patterns
        
        # 门类 patterns - single letter followed by category name
        if re.match(r'^<td><b>[A-Z]</b><td>', line) and i+1 < len(lines):
            section_match = re.search(r'<td><b>([A-Z])</b>', line)
            if section_match:
                next_line = lines[i+1] if i+1 < len(lines) else ""
                name_match = re.search(r'<td>([^<]+)<td>', next_line)
                if name_match:
                    section_code = section_match.group(1)
                    section_name = html.unescape(name_match.group(1).strip())
                    if section_name and section_name not in ['门类', '大类', '中类', '小类', '&nbsp;']:
                        current_section = section_code
                        current_division = ""
                        current_group = ""
                        classifications.append(('门类', section_code, section_name, ''))
        
        # 大类 patterns - two digits
        elif re.search(r'<td><b>([0-9]{2})</b>', line):
            division_match = re.search(r'<td><b>([0-9]{2})</b>', line)
            name_match = re.search(r'<td><b>([^<]+)</b><td>', line)
            if division_match and name_match:
                division_code = division_match.group(1)
                division_name = html.unescape(name_match.group(1).strip())
                if division_name and division_name not in ['门类', '大类', '中类', '小类', '&nbsp;']:
                    current_division = division_code
                    current_group = ""
                    classifications.append(('大类', division_code, division_name, current_section))
        
        # 中类 patterns - three digits  
        elif re.search(r'<td> ([0-9]{3})<td>', line):
            group_match = re.search(r'<td> ([0-9]{3})<td>', line)
            # Look for name in the same line or next lines
            name_match = re.search(r'<td> ([^<]+?)<td>', line)
            if group_match and name_match:
                group_code = group_match.group(1)
                group_name = html.unescape(name_match.group(1).strip())
                if group_name and group_name not in ['门类', '大类', '中类', '小类', '&nbsp;'] and not re.match(r'^[0-9]+$', group_name):
                    current_group = group_code
                    classifications.append(('中类', group_code, group_name, current_division))
        
        # 小类 patterns - four digits
        elif re.search(r'<td> ([0-9]{4})<td>', line):
            class_match = re.search(r'<td> ([0-9]{4})<td>', line)
            name_match = re.search(r'<td> ([^<]+?)<td>', line)
            if class_match and name_match:
                class_code = class_match.group(1)
                class_name = html.unescape(name_match.group(1).strip())
                if class_name and class_name not in ['门类', '大类', '中类', '小类', '&nbsp;'] and not re.match(r'^[0-9]+$', class_name):
                    classifications.append(('小类', class_code, class_name, current_group))
        
        i += 1
    
    return classifications

def manual_extract():
    """Manual extraction based on the patterns I see in the grep output"""
    classifications = [
        # 门类 A
        ('门类', 'A', '农、林、牧、渔业', ''),
        # 大类 01-05
        ('大类', '01', '农业', 'A'),
        ('大类', '02', '林业', 'A'),
        ('大类', '03', '畜牧业', 'A'),
        ('大类', '04', '渔业', 'A'),
        ('大类', '05', '农、林、牧、渔专业及辅助性活动', 'A'),
        
        # 中类和小类 for 01
        ('中类', '011', '谷物种植', '01'),
        ('小类', '0111', '稻谷种植', '011'),
        ('小类', '0112', '小麦种植', '011'),
        ('小类', '0113', '玉米种植', '011'),
        ('小类', '0119', '其他谷物种植', '011'),
        
        ('中类', '012', '豆类、油料和薯类种植', '01'),
        ('小类', '0121', '豆类种植', '012'),
        ('小类', '0122', '油料种植', '012'),
        ('小类', '0123', '薯类种植', '012'),
        
        ('中类', '013', '棉、麻、糖、烟草种植', '01'),
        ('小类', '0131', '棉花种植', '013'),
        ('小类', '0132', '麻类种植', '013'),
        ('小类', '0133', '糖料种植', '013'),
        ('小类', '0134', '烟草种植', '013'),
        
        ('中类', '014', '蔬菜、食用菌及园艺作物种植', '01'),
        ('小类', '0141', '蔬菜种植', '014'),
        ('小类', '0142', '食用菌种植', '014'),
        ('小类', '0143', '花卉种植', '014'),
        ('小类', '0149', '其他园艺作物种植', '014'),
        
        ('中类', '015', '水果种植', '01'),
        ('小类', '0151', '仁果类和核果类水果种植', '015'),
        ('小类', '0152', '葡萄种植', '015'),
        ('小类', '0153', '柑橘类种植', '015'),
        ('小类', '0154', '香蕉等亚热带水果种植', '015'),
        ('小类', '0159', '其他水果种植', '015'),
        
        ('中类', '016', '坚果、含油果、香料和饮料作物种植', '01'),
        ('小类', '0161', '坚果种植', '016'),
        ('小类', '0162', '含油果种植', '016'),
        ('小类', '0163', '香料作物种植', '016'),
        ('小类', '0164', '茶叶种植', '016'),
        ('小类', '0169', '其他饮料作物种植', '016'),
        
        ('中类', '017', '中药材种植', '01'),
        ('小类', '0171', '中草药种植', '017'),
        ('小类', '0179', '其他中药材种植', '017'),
        
        ('中类', '018', '草种植及割草', '01'),
        ('小类', '0181', '草种植', '018'),
        ('小类', '0182', '天然草原割草', '018'),
        
        ('中类', '019', '其他农业', '01'),
        ('小类', '0190', '其他农业', '019'),
        
        # 门类 B
        ('门类', 'B', '采矿业', ''),
        ('大类', '06', '煤炭开采和洗选业', 'B'),
        ('大类', '07', '石油和天然气开采业', 'B'),
        ('大类', '08', '黑色金属矿采选业', 'B'),
        ('大类', '09', '有色金属矿采选业', 'B'),
        ('大类', '10', '非金属矿采选业', 'B'),
        ('大类', '11', '开采专业及辅助性活动', 'B'),
        ('大类', '12', '其他采矿业', 'B'),
        
        # 门类 C
        ('门类', 'C', '制造业', ''),
        ('大类', '13', '农副食品加工业', 'C'),
        ('大类', '14', '食品制造业', 'C'),
        ('大类', '15', '酒、饮料和精制茶制造业', 'C'),
        ('大类', '16', '烟草制品业', 'C'),
        ('大类', '17', '纺织业', 'C'),
        ('大类', '18', '纺织服装、服饰业', 'C'),
        ('大类', '19', '皮革、毛皮、羽毛及其制品和制鞋业', 'C'),
        ('大类', '20', '木材加工和木、竹、藤、棕、草制品业', 'C'),
    ]
    
    return classifications

def format_csv_output(classifications):
    """Format classifications as CSV"""
    output = ['分类级别,分类代码,分类名称,上级代码']
    
    for level, code, name, parent in classifications:
        # Clean up names - remove extra spaces and special characters
        clean_name = re.sub(r'\s+', ' ', name).strip()
        clean_name = clean_name.replace(',', '，')  # Replace comma with Chinese comma
        output.append(f'{level},{code},{clean_name},{parent}')
    
    return '\n'.join(output)

if __name__ == "__main__":
    # Use manual extraction for now since it's more reliable
    classifications = manual_extract()
    csv_output = format_csv_output(classifications)
    
    # Write to file
    with open('/mnt/d/Research/start-up/Classification/gb_t_4754_2017_sample.csv', 'w', encoding='utf-8') as f:
        f.write(csv_output)
    
    # Print sample
    print(csv_output)
    print(f"\n样本数据已生成，共 {len(classifications)} 条记录")
    print("注意：这只是部分数据样本。完整的GB/T 4754-2017标准包含约1380个小类。")