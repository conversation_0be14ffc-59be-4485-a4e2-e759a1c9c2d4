#!/usr/bin/env python3
import csv
import json
import sys

def escape_js_string(s):
    """Escape a string for JavaScript"""
    if s is None or s == '' or s == '#VALUE!':
        return ''
    # Replace problematic characters
    s = str(s)
    s = s.replace('\\', '\\\\')  # Escape backslashes first
    s = s.replace('"', '\\"')   # Escape double quotes
    s = s.replace("'", "\\'")   # Escape single quotes
    s = s.replace('\n', '\\n')  # Escape newlines
    s = s.replace('\r', '\\r')  # Escape carriage returns
    s = s.replace('\t', '\\t')  # Escape tabs
    return s

def process_csv_to_js(csv_file_path):
    """Convert CSV to JavaScript array of objects"""
    data = []
    
    with open(csv_file_path, 'r', encoding='utf-8-sig') as file:
        # Skip BOM if present
        content = file.read()
        if content.startswith('\ufeff'):
            content = content[1:]
        
        # Split into lines and process
        lines = content.strip().split('\n')
        
        # Skip header
        for line in lines[1:]:
            # Parse CSV line manually to handle commas within quotes
            fields = []
            current_field = ''
            in_quotes = False
            
            i = 0
            while i < len(line):
                char = line[i]
                if char == '"':
                    in_quotes = not in_quotes
                elif char == ',' and not in_quotes:
                    fields.append(current_field)
                    current_field = ''
                    i += 1
                    continue
                else:
                    current_field += char
                i += 1
            
            # Add the last field
            fields.append(current_field)
            
            # Ensure we have enough fields
            while len(fields) < 11:
                fields.append('')
            
            # Extract data according to the column structure
            level_zh = escape_js_string(fields[0])
            level_en = escape_js_string(fields[1])
            code_zh = escape_js_string(fields[2])
            code = escape_js_string(fields[3])
            name_zh = escape_js_string(fields[4])
            name_en = escape_js_string(fields[5])
            parent_code_zh = escape_js_string(fields[6])
            parent_code = escape_js_string(fields[7])
            keywords_zh = escape_js_string(fields[8])
            keywords_en = escape_js_string(fields[9])
            note = escape_js_string(fields[10])
            
            # Create object
            obj = {
                'level': level_en if level_en else level_zh,
                'code': code if code else code_zh,
                'nameZh': name_zh,
                'nameEn': name_en,
                'parentCode': parent_code if parent_code else parent_code_zh,
                'keywords': keywords_en if keywords_en else keywords_zh,
                'note': note
            }
            
            data.append(obj)
    
    return data

def generate_compact_js_variable(data):
    """Generate compact JavaScript variable declaration"""
    js_objects = []
    
    for item in data:
        # Create a more compact representation
        js_obj = '{'
        js_obj += f'level:"{item["level"]}",'
        js_obj += f'code:"{item["code"]}",'
        js_obj += f'nameZh:"{item["nameZh"]}",'
        js_obj += f'nameEn:"{item["nameEn"]}",'
        js_obj += f'parentCode:"{item["parentCode"]}",'
        js_obj += f'keywords:"{item["keywords"]}",'
        js_obj += f'note:"{item["note"]}"'
        js_obj += '}'
        js_objects.append(js_obj)
    
    # Create compact format
    js_declaration = "const csvData=["
    js_declaration += ",".join(js_objects)
    js_declaration += "];"
    
    return js_declaration

if __name__ == "__main__":
    csv_file = "/mnt/d/Research/start-up/Classification/final_data/GB_T_4754_2017/GB_T_4754_2017_zh_en.csv"
    
    try:
        data = process_csv_to_js(csv_file)
        js_code = generate_compact_js_variable(data)
        print(js_code)
    except Exception as e:
        print(f"Error: {e}", file=sys.stderr)
        sys.exit(1)