#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import re

def thorough_clean():
    """
    彻底清理翻译结果
    """
    print("开始彻底清理翻译结果...")
    
    # 读取AI翻译的文件
    df = pd.read_csv("GB_T_4754_2017_converted_english_AI.csv", encoding='utf-8')
    print(f"读取到 {len(df)} 行数据")
    
    # 深度清理每一列
    df['Classification Level'] = df['Classification Level'].apply(clean_classification_level)
    df['Classification Name'] = df['Classification Name'].apply(deep_clean_text)
    df['Core Keywords'] = df['Core Keywords'].apply(clean_keywords)
    df['Description'] = df['Description'].apply(deep_clean_text)
    
    # 保存最终结果
    output_file = "GB_T_4754_2017_converted_english_CLEAN.csv"
    df.to_csv(output_file, index=False, encoding='utf-8')
    print(f"彻底清理完成！已保存到 {output_file}")
    print(f"共处理 {len(df)} 行数据")
    
    return output_file

def clean_classification_level(text):
    """
    清理分类级别
    """
    if pd.isna(text) or str(text) == 'nan':
        return ''
    
    text_str = str(text).strip()
    
    # 精确映射
    mappings = {
        'Sector': 'Sector',
        'Major Category': 'Major Category',
        'Medium Category': 'Medium Category',
        'Minor Category': 'Minor Category'
    }
    
    return mappings.get(text_str, text_str)

def clean_keywords(text):
    """
    清理关键词
    """
    if pd.isna(text) or str(text) == 'nan':
        return ''
    
    text_str = str(text)
    
    # 如果包含分号，分别处理
    if ';' in text_str:
        keywords = text_str.split(';')
        cleaned_keywords = []
        for keyword in keywords:
            cleaned = deep_clean_text(keyword.strip())
            if cleaned:
                cleaned_keywords.append(cleaned)
        return ';'.join(cleaned_keywords)
    else:
        return deep_clean_text(text_str)

def deep_clean_text(text):
    """
    深度清理文本
    """
    if pd.isna(text) or str(text) == 'nan':
        return ''
    
    result = str(text).strip()
    
    if not result:
        return ''
    
    # 第一轮：清理混合词汇
    mixed_word_fixes = {
        # 制造业相关
        'manufacturing': 'Manufacturing',
        'productionmanufacturing': 'Production Manufacturing',
        '工业manufacturing': 'Industrial Manufacturing',
        '水泥制品manufacturing': 'Cement Product Manufacturing',
        '砼结构构件manufacturing': 'Concrete Structure Component Manufacturing',
        '石cotton水泥制品manufacturing': 'Asbestos Cement Product Manufacturing',
        '轻质建筑材料manufacturing': 'Lightweight Building Material Manufacturing',
        'other水泥类似制品manufacturing': 'Other Cement-like Product Manufacturing',
        '砖瓦': 'Brick and Tile',
        '石材etc.建筑材料manufacturing': 'Stone and Other Building Material Manufacturing',
        '粘土砖瓦': 'Clay Brick and Tile',
        '建筑砌块manufacturing': 'Building Block Manufacturing',
        '建筑用石processing': 'Building Stone Processing',
        '防水建筑材料manufacturing': 'Waterproof Building Material Manufacturing',
        '隔热': 'Insulation',
        '隔音材料manufacturing': 'Sound Insulation Material Manufacturing',
        
        # 服务业相关
        'services业': 'Service Industry',
        '商业services': 'Commercial Services',
        '宗教团体services': 'Religious Organization Services',
        '宗教activities场所services': 'Religious Venue Services',
        'managementactivities': 'Management Activities',
        
        # 组织相关
        '宗教组织': 'Religious Organizations',
        '基层群众自治组织': 'Grassroots Mass Autonomous Organizations',
        'other组织': 'Other Organizations',
        '社区居民自治组织': 'Community Residents\' Autonomous Organizations',
        '村民自治组织': 'Villagers\' Autonomous Organizations',
        'International Organizations': 'International Organizations',
        '国际合作': 'International Cooperation',
        
        # 通用修复
        'etc.': 'etc.',
        ' and ': ' and ',
        'for主要原料': 'as main raw materials',
        'for本地区': 'for local areas',
        'not elsewhere classified': 'not elsewhere classified',
        'andother': 'and other',
        'andothernot': 'and other not',
        'but不including': 'but not including',
        'or': 'or',
        'activities': 'activities',
        'production': 'production',
        'processing': 'processing',
        'management': 'management',
        
        # 地理和场所
        '民政部门': 'civil affairs departments',
        '登记': 'registration of',
        '宗教团体': 'religious organizations',
        'in政府': 'in government',
        '宗教事务部门': 'religious affairs departments',
        '场所': 'venues',
        '通过选举': 'through elections',
        '产生': 'established',
        '社区性组织': 'community organizations',
        '该组织': 'these organizations',
        '提供': 'provide',
        '一般性': 'general',
        '调解': 'mediation',
        '治安': 'public security',
        '优抚': 'preferential treatment',
        '计划生育': 'family planning',
        '城市': 'urban',
        '镇居民': 'town residents',
        '群众性': 'mass',
        '自治组织': 'autonomous organizations',
        '农村': 'rural',
        '村民': 'villagers',
        '联合国': 'United Nations',
        '驻我国': 'stationed in China',
        '境内': 'within China',
        '机构': 'institutions',
        
        # 数字和符号修复
        'this sector includes01-05大类': 'This sector includes major categories 01-05',
        'this sector includes06-12大类': 'This sector includes major categories 06-12',
        'this sector includes13-43大类': 'This sector includes major categories 13-43',
        'this sector includes97大类': 'This sector includes major category 97',
        'this category includes': 'This category includes',
        'refers to': 'Refers to',
        
        # 常见中文词汇
        '指': 'Refers to',
        '包括': 'including',
        '以及': 'and',
        '等': 'etc.',
        '各种': 'various',
        '其他': 'other',
        '主要': 'main',
        '用于': 'used for',
        '进行': 'conducting',
        '提供': 'providing',
        '从事': 'engaging in',
        '生产': 'production',
        '制造': 'manufacturing',
        '加工': 'processing',
        '服务': 'services',
        '活动': 'activities',
        '管理': 'management',
        '经营': 'operation',
        '建筑': 'construction',
        '工程': 'engineering',
        '施工': 'construction',
        '材料': 'materials',
        '产品': 'products',
        '制品': 'products',
        '设备': 'equipment',
        '系统': 'systems',
        '技术': 'technology',
        '方法': 'methods',
        '过程': 'processes',
        '工艺': 'processes',
        '原料': 'raw materials',
        '成品': 'finished products',
        '半成品': 'semi-finished products',
        '零件': 'parts',
        '部件': 'components',
        '装置': 'devices',
        '机械': 'machinery',
        '设施': 'facilities',
        '场所': 'venues',
        '地点': 'locations',
        '区域': 'areas',
        '范围': 'scope',
        '领域': 'fields',
        '行业': 'industries',
        '企业': 'enterprises',
        '公司': 'companies',
        '机构': 'institutions',
        '组织': 'organizations',
        '团体': 'groups',
        '协会': 'associations',
        '联合会': 'federations',
        '委员会': 'committees',
        '部门': 'departments',
        '单位': 'units',
        '中心': 'centers',
        '基地': 'bases',
        '园区': 'parks',
        '平台': 'platforms',
        '网络': 'networks',
        
        # 清理多余空格和标点
        '  ': ' ',
        '   ': ' ',
        '    ': ' ',
        ' ,': ',',
        ' .': '.',
        ' ;': ';',
        ' :': ':',
        ' !': '!',
        ' ?': '?',
        '( ': '(',
        ' )': ')',
        '[ ': '[',
        ' ]': ']',
        ', ': ', ',
        '. ': '. ',
        '; ': '; ',
        ': ': ': ',
    }
    
    # 应用修复
    for old, new in sorted(mixed_word_fixes.items(), key=lambda x: len(x[0]), reverse=True):
        if old in result:
            result = result.replace(old, new)
    
    # 第二轮：使用正则表达式清理
    # 移除单独的中文字符（保留词组）
    result = re.sub(r'(?<![a-zA-Z])[一-龟](?![一-龟])', '', result)
    
    # 清理连续空格
    result = re.sub(r'\s+', ' ', result)
    
    # 清理首尾空格
    result = result.strip()
    
    # 第三轮：句首字母大写
    if result and len(result) > 0:
        result = result[0].upper() + result[1:] if len(result) > 1 else result.upper()
    
    return result

if __name__ == "__main__":
    thorough_clean()