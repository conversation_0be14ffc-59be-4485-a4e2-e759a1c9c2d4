#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GB/T 4754-2017 分类数据格式转换脚本
将用户修正的分类数据转换为原始格式
正确处理CSV格式中的名称和说明列
"""

import csv
import os
import re

def convert_classification_format(input_file, output_file):
    """
    转换分类数据格式
    从分离列格式转换为原始格式
    """
    print(f"正在读取文件: {input_file}")
    
    converted_data = []
    
    # 使用CSV reader读取文件
    with open(input_file, 'r', encoding='utf-8-sig') as f:
        reader = csv.reader(f)
        lines = list(reader)
    
    print(f"原始文件行数: {len(lines)}")
    
    # 跳过前两行标题
    data_lines = lines[2:]  # 跳过标题行和列名行
    
    print(f"数据行数: {len(data_lines)}")
    
    # 处理每一行数据
    current_section = ''
    current_division = ''
    current_group = ''
    
    for line_num, row in enumerate(data_lines):
        if not row or len(row) < 6:
            continue
            
        # 获取各级代码
        section = clean_value(row[0])      # 门类
        division = clean_value(row[1])     # 大类  
        group = clean_value(row[2])        # 中类
        class_code = clean_value(row[3])   # 小类
        name = clean_value(row[4])         # 类别名称
        description = clean_value(row[5])  # 说明
        
        # 处理名称前的空格和特殊字符
        if name.startswith('\xa0'):
            name = name.strip('\xa0 ')
        
        # 处理说明前的空格和特殊字符
        if description.startswith('\xa0'):
            description = description.strip('\xa0 ')
        
        # 跳过空行
        if not name:
            continue
            
        # 确定分类级别和代码
        classification_level = ''
        classification_code = ''
        parent_code = ''
        core_keywords = ''
        
        if section and not division and not group and not class_code:
            # 门类级别
            classification_level = '门类'
            classification_code = section
            parent_code = ''
            core_keywords = generate_section_keywords(name)
            current_section = section
                
        elif division and not group and not class_code:
            # 大类级别
            classification_level = '大类'
            classification_code = format_code(division, 2)
            parent_code = current_section
            core_keywords = generate_keywords_from_name(name)
            current_division = format_code(division, 2)
            
        elif group and not class_code:
            # 中类级别
            classification_level = '中类'
            classification_code = format_code(group, 3)
            parent_code = current_division
            core_keywords = generate_keywords_from_name(name)
            current_group = format_code(group, 3)
            
        elif class_code:
            # 小类级别
            classification_level = '小类'
            classification_code = format_code(class_code, 4)
            parent_code = current_group
            core_keywords = generate_keywords_from_name(name)
        
        # 添加到结果列表
        if classification_code and name:
            converted_data.append({
                '分类级别': classification_level,
                '分类代码': classification_code,
                '分类名称': name,
                '上级代码': parent_code,
                '核心关键词': core_keywords,
                '说明': description
            })
            
            if line_num < 20:  # 显示前20条的转换信息
                print(f"行 {line_num+3}: {classification_level} {classification_code} {name} | {description}")
    
    print(f"转换后数据行数: {len(converted_data)}")
    
    # 保存转换后的数据
    fieldnames = ['分类级别', '分类代码', '分类名称', '上级代码', '核心关键词', '说明']
    with open(output_file, 'w', encoding='utf-8-sig', newline='') as f:
        writer = csv.DictWriter(f, fieldnames=fieldnames)
        writer.writeheader()
        writer.writerows(converted_data)
    
    print(f"数据已保存到: {output_file}")
    return converted_data

def clean_value(value):
    """清理数据值"""
    if value is None:
        return ''
    value = str(value).strip()
    if value in ['nan', 'NaN', 'None', '']:
        return ''
    return value

def format_code(code, length):
    """格式化代码"""
    if not code:
        return ''
    
    # 如果是纯数字，格式化为指定长度
    if code.isdigit():
        return f"{int(code):0{length}d}"
    else:
        return code

def generate_section_keywords(name):
    """为门类生成关键词"""
    keywords_map = {
        '农': '农业生产;种植业;畜牧业;林业;渔业;农产品',
        '采矿': '矿物开采;煤炭;石油;天然气;金属矿;非金属矿',
        '制造': '制造加工;生产制造;工业生产;加工业;制造企业',
        '电力': '电力;热力;燃气;水;公用事业',
        '建筑': '建筑工程;土木工程;房屋建筑;基础设施',
        '批发': '批发贸易;零售贸易;商品流通;销售',
        '零售': '批发贸易;零售贸易;商品流通;销售',
        '交通': '交通运输;仓储;邮政;物流',
        '住宿': '住宿服务;餐饮服务;酒店;餐厅',
        '信息': '信息技术;软件;互联网;通信',
        '金融': '金融服务;银行;保险;证券;投资',
        '房地产': '房地产开发;物业管理;房屋租赁',
        '租赁': '租赁服务;商务服务;企业服务',
        '科学': '科学研究;技术服务;专业服务',
        '水利': '水利管理;环境治理;公共设施',
        '居民': '居民服务;修理服务;其他服务',
        '教育': '教育服务;培训;学校教育',
        '卫生': '卫生医疗;社会工作;健康服务',
        '文化': '文化艺术;体育娱乐;传媒',
        '公共管理': '公共管理;社会保障;政府服务',
        '国际': '国际组织;国际合作'
    }
    
    for key, keywords in keywords_map.items():
        if key in name:
            return keywords
    
    return '其他服务;未分类'

def generate_keywords_from_name(name):
    """根据分类名称生成关键词"""
    keywords = []
    
    # 基于名称内容生成关键词
    keyword_rules = [
        ('种植', ['种植业', '农作物', '作物生产']),
        ('养殖', ['养殖业', '畜牧业', '动物饲养']),
        ('饲养', ['养殖业', '畜牧业', '动物饲养']),
        ('加工', ['加工业', '生产加工', '制造']),
        ('开采', ['开采', '矿业', '资源开采']),
        ('制造', ['制造业', '生产制造', '工业制造']),
        ('服务', ['服务业', '商业服务']),
        ('管理', ['管理', '行政管理']),
        ('技术', ['技术服务', '专业技术']),
        ('交通', ['交通', '运输']),
        ('教育', ['教育', '培训', '教学']),
        ('医疗', ['医疗', '卫生', '健康']),
        ('卫生', ['医疗', '卫生', '健康']),
        ('金融', ['金融', '银行', '投资']),
        ('建筑', ['建筑', '工程', '施工']),
        ('运输', ['运输', '交通', '物流']),
        ('贸易', ['贸易', '商业', '销售']),
        ('零售', ['零售', '销售', '商业']),
        ('批发', ['批发', '贸易', '销售'])
    ]
    
    for rule, rule_keywords in keyword_rules:
        if rule in name:
            keywords.extend(rule_keywords)
    
    # 添加名称本身的关键部分
    key_parts = []
    separators = ['、', '，', '和', '及', '与', '或']
    
    temp_name = name
    for sep in separators:
        if sep in temp_name:
            key_parts.extend([part.strip() for part in temp_name.split(sep) if part.strip()])
            break
    else:
        key_parts.append(name)
    
    keywords.extend(key_parts)
    
    # 去重并限制数量
    seen = set()
    unique_keywords = []
    for kw in keywords:
        if kw not in seen and kw:
            unique_keywords.append(kw)
            seen.add(kw)
            if len(unique_keywords) >= 8:
                break
    
    return ';'.join(unique_keywords)

if __name__ == "__main__":
    input_file = "/mnt/d/Research/start-up/Classification/GB_T_4754_2017_corrected.csv"
    output_file = "/mnt/d/Research/start-up/Classification/GB_T_4754_2017_converted.csv"
    
    if not os.path.exists(input_file):
        print(f"错误：输入文件不存在: {input_file}")
        exit(1)
    
    try:
        result_data = convert_classification_format(input_file, output_file)
        print("\n转换完成！")
        print(f"成功转换 {len(result_data)} 条记录")
        
        # 显示前几行作为示例
        print("\n转换结果示例:")
        for i, row in enumerate(result_data[:10]):
            print(f"{i+1:2d}. {row['分类级别']} {row['分类代码']} {row['分类名称']}")
        
        if len(result_data) > 10:
            print(f"... 还有 {len(result_data) - 10} 条记录")
            
    except Exception as e:
        print(f"转换过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()