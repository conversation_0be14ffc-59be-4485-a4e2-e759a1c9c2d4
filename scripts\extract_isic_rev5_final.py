import pandas as pd
import os
import re

def extract_isic_rev5_data():
    """
    Extract ISIC Rev.5 classification data and convert to format similar to GB_T_4754_2017_converted.csv
    """
    
    # Check ISIC file paths
    isic_paths = [
        'raw_data/ISIC5_Exp_Notes_11Mar2024.xlsx',
        'final_data/ISIC_Rev5/ISIC5_Exp_Notes_11Mar2024.xlsx'
    ]
    
    isic_file = None
    for path in isic_paths:
        if os.path.exists(path):
            isic_file = path
            break
    
    if not isic_file:
        print("ISIC5_Exp_Notes_11Mar2024.xlsx file not found")
        return
    
    print(f"Reading file: {isic_file}")
    
    # Read all sheets from Excel file
    excel_file = pd.ExcelFile(isic_file)
    print(f"Excel file contains sheets: {excel_file.sheet_names}")
    
    # Try to read main classification data sheet
    main_sheets = ['ISIC Rev.5', 'Classification', 'Structure', 'Codes', 'ISIC5']
    data_sheet = None
    
    for sheet in main_sheets:
        if sheet in excel_file.sheet_names:
            data_sheet = sheet
            break
    
    if not data_sheet:
        # If no expected sheet name found, use first sheet
        data_sheet = excel_file.sheet_names[0]
    
    print(f"Using sheet: {data_sheet}")
    
    # Read data
    df = pd.read_excel(isic_file, sheet_name=data_sheet)
    print(f"Data shape: {df.shape}")
    print(f"Column names: {df.columns.tolist()}")
    print("\nFirst 5 rows:")
    print(df.head())
    
    # Determine classification level based on ISIC code length
    def get_classification_level(code):
        if pd.isna(code):
            return None
        code_str = str(code).strip()
        if len(code_str) == 1:
            return "Section"  # Section (A, B, C, etc.)
        elif len(code_str) == 3:
            return "Division"  # Division (A01, A02, etc.)
        elif len(code_str) == 4:
            return "Group"  # Group (A011, A012, etc.)
        elif len(code_str) == 5:
            return "Class"  # Class (A0111, A0112, etc.)
        else:
            return "Other"
    
    def get_parent_code(code):
        if pd.isna(code):
            return ""
        code_str = str(code).strip()
        if len(code_str) <= 1:
            return ""
        else:
            return code_str[:-1]
    
    def clean_text(text):
        """Clean text by removing special characters and formatting"""
        if pd.isna(text):
            return ""
        text = str(text)
        # Remove _x000D_ and other special characters
        text = re.sub(r'_x[0-9A-Fa-f]+_', ' ', text)
        # Replace newlines with spaces
        text = re.sub(r'[\r\n]+', ' ', text)
        # Remove bullet points and special characters
        text = re.sub(r'[•◦]', '-', text)
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text)
        # Remove leading/trailing whitespace
        text = text.strip()
        return text
    
    def extract_keywords(title):
        """Extract keywords from title"""
        if pd.isna(title):
            return ""
        title = str(title)
        # Split by common separators and clean
        keywords = re.split(r'[,;]', title)
        keywords = [kw.strip() for kw in keywords if kw.strip()]
        return '; '.join(keywords)
    
    # Determine column mapping
    code_col = 'ISIC Rev 5 Code (with Section)'  # Use the full code with section letter
    title_col = 'ISIC Rev 5 Title'
    desc_col = 'ISIC Rev 5 Includes'
    
    print(f"Using column mapping: Code={code_col}, Title={title_col}, Description={desc_col}")
    
    result_data = []
    
    # Process data
    for _, row in df.iterrows():
        if code_col in df.columns and title_col in df.columns:
            code = row[code_col]
            title = row[title_col]
            description = row[desc_col] if desc_col in df.columns else ""
            
            if pd.notna(code) and pd.notna(title):
                level = get_classification_level(code)
                parent_code = get_parent_code(code)
                
                # Extract and clean keywords
                keywords = extract_keywords(title)
                
                # Clean description
                clean_description = clean_text(description)
                
                result_data.append({
                    'Classification Level': level,
                    'Classification Code': str(code).strip(),
                    'Classification Name': clean_text(title),
                    'Parent Code': parent_code,
                    'Core Keywords': keywords,
                    'Description': clean_description
                })
    
    # Create result DataFrame
    result_df = pd.DataFrame(result_data)
    
    # Ensure output directory exists
    output_dir = 'final_data/ISIC_Rev5'
    os.makedirs(output_dir, exist_ok=True)
    
    # Save result with proper CSV handling
    output_file = f'{output_dir}/ISIC_Rev5_converted.csv'
    result_df.to_csv(output_file, index=False, encoding='utf-8-sig', quoting=1)
    
    print(f"\nConversion completed!")
    print(f"Output file: {output_file}")
    print(f"Total processed records: {len(result_df)}")
    
    # Statistics by classification level
    level_counts = result_df['Classification Level'].value_counts()
    print(f"\nLevel statistics:")
    for level, count in level_counts.items():
        print(f"  {level}: {count} records")
    
    print(f"\nResult preview:")
    print(result_df.head(10))
    
    return result_df

if __name__ == "__main__":
    extract_isic_rev5_data()
